1. [x] #15/07 Synergia invoice paid $99 - I have payment advice but no invoice This has been asked for previously a couple of times.  [completion:: 2025-08-21]
	1. [x] This is printed out and added to data entry tray. Also emailed to <PERSON><PERSON> 21st aug  [completion:: 2025-08-21]

2. [x] Pay slip #finance-27/07/2025 You have given me the direct debit transfer advice, this is irrelevant when coding as it only shows the total. I need the pay slip that shows net wage, super and work cover break up.  [completion:: 2025-08-21]

3. [x] Eftpos receipt #finance-03/08/2025  The total received was $95. Yet I have 2 data entry documents 1 for tickets and another 1 for offering. There is more than enough room to record all the transactions on 1 sheet of paper. I have mentioned this before.  [completion:: 2025-08-21]
> 		added info to [[Finance-Procedures]]

4. [x] Eftpos receipt #finance-10/08/205 again this is divided into 2 sheets of paper - when only 1 was necessary. The amount total must equal the amount banked.  [completion:: 2025-08-21]
		- added info to [[Finance-Procedures]]

5. [x] Eftpos 27/07 $227 <PERSON><PERSON><PERSON> has corrected to divide the amounts between pie drive and offering.  [completion:: 2025-08-21]
	1. [x] The banking was $227 - $17 for pies and the balance for offering.  [completion:: 2025-08-21]
> 		` Funds received voucher completed with items`

6. [x] Stapled to the water bill was the telstra payment advice, another copy was then stapled to the telstra account - a waste of paper and time.  [completion:: 2025-08-21]
> 		*Advice*

7. [x] WLTTW - invoice and payment slip was not in the items collected by Peter on Thursday, the payment date was 15/08.  [completion:: 2025-08-21]
> 		Payment was made and slip was printed. Will search for the slip. It may be with you now