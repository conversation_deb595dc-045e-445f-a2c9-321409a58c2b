<!doctype html>
<html>
    <head>
        <title>volunteer_absence_analysis_summary</title>
        <meta charset='utf-8'/>
        <style>
 .ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0; overflow-anchor: none;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; min-height: 100%; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #ddd;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor {display: block;}
.ͼ1 .cm-iso {unicode-bidi: isolate;}
.ͼ1 .cm-announced {position: fixed; top: -10000px;}
@media print {.ͼ1 .cm-announced {display: none;}}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; inset-inline-start: 0; z-index: 200;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border-right: 1px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0; z-index: 300;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top;}
.ͼ1 .cm-highlightSpace {background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%); background-position: center;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ1 .cm-foldPlaceholder {background-color: #eee; border: 1px solid #ddd; color: #888; border-radius: .2em; margin: 0 1px; padding: 0 1px; cursor: pointer;}
.ͼ1 .cm-foldGutter span {padding: 0 1px; cursor: pointer;}
.ͼp .cm-vimMode .cm-cursorLayer:not(.cm-vimCursorLayer) {display: none;}
.ͼp .cm-vim-panel {padding: 0px 10px; font-family: monospace; min-height: 1.3em;}
.ͼp .cm-vim-panel input {background: transparent; border: none; outline: none;}
.ͼo .cm-vimMode .cm-line {caret-color: transparent !important;}
.ͼo .cm-fat-cursor {position: absolute; border: none; white-space: pre;}
.ͼo.cm-focused > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {background: var(--interactive-accent); color: var(--text-on-accent);}
.ͼo:not(.cm-focused) > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {color: transparent !important;}
 @keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.workspace-leaf-content[data-type="git-view"] .button-border {
    border: 2px solid var(--interactive-accent);
    border-radius: var(--radius-s);
}

.workspace-leaf-content[data-type="git-view"] .view-content {
    padding: 0;
}

.workspace-leaf-content[data-type="git-history-view"] .view-content {
    padding: 0;
}

.loading > svg {
    animation: 2s linear infinite loading;
    transform-origin: 50% 50%;
    display: inline-block;
}

.obsidian-git-center {
    margin: auto;
    text-align: center;
    width: 50%;
}

.obsidian-git-textarea {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.obsidian-git-disabled {
    opacity: 0.5;
}

.obsidian-git-center-button {
    display: block;
    margin: 20px auto;
}

.tooltip.mod-left {
    overflow-wrap: break-word;
}

.tooltip.mod-right {
    overflow-wrap: break-word;
}
.git-tools {
    display: flex;
    margin-left: auto;
}
.git-tools .type {
    padding-left: var(--size-2-1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
}

.git-tools .type[data-type="M"] {
    color: orange;
}
.git-tools .type[data-type="D"] {
    color: red;
}
.git-tools .buttons {
    display: flex;
}
.git-tools .buttons > * {
    padding: 0 0;
    height: auto;
}

.is-active .git-tools .buttons > * {
    color: var(--nav-item-color-active);
}

.git-author {
    color: var(--text-accent);
}

.git-date {
    color: var(--text-accent);
}

.git-ref {
    color: var(--text-accent);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-d-none {
    display: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-wrapper {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header {
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--interactive-accent);
    font-family: var(--font-monospace);
    height: 35px;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header,
.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    font-size: 14px;
    margin-left: auto;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-added {
    border: 1px solid #b4e2b4;
    border-radius: 5px 0 0 5px;
    color: #399839;
    padding: 2px;
    text-align: right;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-deleted {
    border: 1px solid #e9aeae;
    border-radius: 0 5px 5px 0;
    color: #c33;
    margin-left: 1px;
    padding: 2px;
    text-align: left;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name-wrapper {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 15px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-wrapper {
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    margin-bottom: 1em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    cursor: pointer;
    display: none;
    font-size: 12px;
    justify-content: flex-end;
    padding: 4px 8px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse.d2h-selected {
    background-color: #c8e1ff;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse-input {
    margin: 0 4px 0 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-table {
    border-collapse: collapse;
    font-family: Menlo, Consolas, monospace;
    font-size: 13px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-files-diff {
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-diff {
    overflow-y: hidden;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-side-diff {
    display: inline-block;
    margin-bottom: -8px;
    margin-right: -4px;
    overflow-x: scroll;
    overflow-y: hidden;
    width: 50%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line {
    padding: 0 8em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    padding: 0 4.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-ctn {
    word-wrap: normal;
    background: none;
    display: inline-block;
    padding: 0;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    vertical-align: middle;
    white-space: pre;
    width: 100%;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #ffb6ba;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #8d232881;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
    border-radius: 0.2em;
    display: inline-block;
    margin-top: -1px;
    text-decoration: none;
    vertical-align: middle;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #97f295;
    text-align: left;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #1d921996;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix {
    word-wrap: normal;
    background: none;
    display: inline;
    padding: 0;
    white-space: pre;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1 {
    float: left;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1,
.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 0.5em;
    text-overflow: ellipsis;
    width: 3.5em;
}

.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    float: right;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    position: absolute;
    text-align: right;
    width: 7.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    padding: 0 0.5em;
    position: absolute;
    text-align: right;
    text-overflow: ellipsis;
    width: 4em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-tbody tr {
    position: relative;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-emptyplaceholder,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    direction: rtl;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #fee8e9;
    border-color: #e9aeae;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: #dfd;
    border-color: #b4e2b4;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #521b1d83;
    border-color: #691d1d73;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: rgba(30, 71, 30, 0.5);
    border-color: #13501381;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-info {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
    color: var(--text-normal);
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #fdf2d0;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #55492480;
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: #ded;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: rgba(37, 78, 37, 0.418);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper {
    margin-bottom: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a {
    color: #3572b0;
    text-decoration: none;
}

.workspace-leaf-content[data-type="diff-view"]
    .d2h-file-list-wrapper
    a:visited {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-header {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-title {
    font-weight: 700;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li {
    border-bottom: 1px solid var(--background-modifier-border);
    margin: 0;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li:last-child {
    border-bottom: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-switch {
    cursor: pointer;
    display: none;
    font-size: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-icon {
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted {
    color: #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added {
    color: #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed {
    color: #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-tag {
    background-color: var(--background-primary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 10px;
    margin-left: 5px;
    padding: 0 2px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted-tag {
    border: 2px solid #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added-tag {
    border: 1px solid #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed-tag {
    border: 1px solid #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved-tag {
    border: 1px solid #3572b0;
}

/* ====================== Line Authoring Information ====================== */

.cm-gutterElement.obs-git-blame-gutter {
    /* Add background color to spacing inbetween and around the gutter for better aesthetics */
    border-width: 0px 2px 0.2px 2px;
    border-style: solid;
    border-color: var(--background-secondary);
    background-color: var(--background-secondary);
}

.cm-gutterElement.obs-git-blame-gutter > div,
.line-author-settings-preview {
    /* delegate text color to settings */
    color: var(--obs-git-gutter-text);
    font-family: monospace;
    height: 100%; /* ensure, that age-based background color occupies entire parent */
    text-align: right;
    padding: 0px 6px 0px 6px;
    white-space: pre; /* Keep spaces and do not collapse them. */
}

@media (max-width: 800px) {
    /* hide git blame gutter not to superpose text */
    .cm-gutterElement.obs-git-blame-gutter {
        display: none;
    }
}

.git-unified-diff-view,
.git-split-diff-view .cm-deletedLine .cm-changedText {
    background-color: #ee443330;
}

.git-unified-diff-view,
.git-split-diff-view .cm-insertedLine .cm-changedText {
    background-color: #22bb2230;
}

/* Limits the scrollbar to the view body */
.git-view {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
}

.git-obscure-prompt[git-is-obscured="true"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg>');
}

.git-obscure-prompt[git-is-obscured="false"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye-off"><path d="M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"></path><path d="M14.084 14.158a3 3 0 0 1-4.242-4.242"></path><path d="M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"></path><path d="m2 2 20 20"></path></svg>');
}

/* Override styling of Codemirror merge view "collapsed lines" indicator */
.git-split-diff-view .ͼ2 .cm-collapsedLines {
    background: var(--interactive-normal);
    border-radius: var(--radius-m);
    color: var(--text-accent);
    font-size: var(--font-small);
    padding: var(--size-4-1) var(--size-4-1);
}
.git-split-diff-view .ͼ2 .cm-collapsedLines:hover {
    background: var(--interactive-hover);
    color: var(--text-accent-hover);
}
 .block-language-dataview {
    overflow-y: auto;
}

/*****************/
/** Table Views **/
/*****************/

/* List View Default Styling; rendered internally as a table. */
.table-view-table {
    width: 100%;
}

.table-view-table > thead > tr, .table-view-table > tbody > tr {
    margin-top: 1em;
    margin-bottom: 1em;
    text-align: left;
}

.table-view-table > tbody > tr:hover {
    background-color: var(--table-row-background-hover);
}

.table-view-table > thead > tr > th {
    font-weight: 700;
    font-size: larger;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: solid;

    max-width: 100%;
}

.table-view-table > tbody > tr > td {
    text-align: left;
    border: none;
    font-weight: 400;
    max-width: 100%;
}

.table-view-table ul, .table-view-table ol {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Rendered value styling for any view. */
.dataview-result-list-root-ul {
    padding: 0em !important;
    margin: 0em !important;
}

.dataview-result-list-ul {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Generic grouping styling. */
.dataview.result-group {
    padding-left: 8px;
}

/*******************/
/** Inline Fields **/
/*******************/

.dataview.inline-field-key {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-primary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-standalone-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

/***************/
/** Task View **/
/***************/

.dataview.task-list-item, .dataview.task-list-basic-item {
    margin-top: 3px;
    margin-bottom: 3px;
    transition: 0.4s;
}

.dataview.task-list-item:hover, .dataview.task-list-basic-item:hover {
    background-color: var(--text-selection);
    box-shadow: -40px 0 0 var(--text-selection);
    cursor: pointer;
}

/*****************/
/** Error Views **/
/*****************/

div.dataview-error-box {
    width: 100%;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px dashed var(--background-secondary);
}

.dataview-error-message {
    color: var(--text-muted);
    text-align: center;
}

/*************************/
/** Additional Metadata **/
/*************************/

.dataview.small-text {
    font-size: smaller;
    color: var(--text-muted);
    margin-left: 3px;
}

.dataview.small-text::before {
	content: "(";
}

.dataview.small-text::after {
	content: ")";
}
 .templater_search {
    width: calc(100% - 20px);
}

.templater_div {
    border-top: 1px solid var(--background-modifier-border);
}

.templater_div > .setting-item {
    border-top: none !important;
    align-self: center;
}

.templater_div > .setting-item > .setting-item-control {
    justify-content: space-around;
    padding: 0;
    width: 100%;
}

.templater_div
    > .setting-item
    > .setting-item-control
    > .setting-editor-extra-setting-button {
    align-self: center;
}

.templater_donating {
    margin: 10px;
}

.templater_title {
    margin: 0;
    padding: 0;
    margin-top: 5px;
    text-align: center;
}

.templater_template {
    align-self: center;
    margin-left: 5px;
    margin-right: 5px;
    width: 70%;
}

.templater_cmd {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    width: 100%;
}

.templater_div2 > .setting-item {
    align-content: center;
    justify-content: center;
}

.templater-prompt-div {
    display: flex;
}

.templater-prompt-form {
    display: flex;
    flex-grow: 1;
}

.templater-prompt-input {
    flex-grow: 1;
}

.templater-button-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

textarea.templater-prompt-input {
    height: 10rem;
}

textarea.templater-prompt-input:focus {
    border-color: var(--interactive-accent);
}

.cm-s-obsidian .templater-command-bg {
    left: 0px;
    right: 0px;
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command {
    font-size: 0.85em;
    font-family: var(--font-monospace);
    line-height: 1.3;
}

.cm-s-obsidian .templater-inline .cm-templater-command {
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command.cm-templater-opening-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-closing-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-interpolation-tag {
    color: var(--code-property, #008bff);
}

.cm-s-obsidian .cm-templater-command.cm-templater-execution-tag {
    color: var(--code-function, #c0d700);
}

.cm-s-obsidian .cm-templater-command.cm-keyword {
    color: var(--code-keyword, #00a7aa);
    font-weight: normal;
}

.cm-s-obsidian .cm-templater-command.cm-atom {
    color: var(--code-normal, #f39b35);
}

.cm-s-obsidian .cm-templater-command.cm-value,
.cm-s-obsidian .cm-templater-command.cm-number,
.cm-s-obsidian .cm-templater-command.cm-type {
    color: var(--code-value, #a06fca);
}

.cm-s-obsidian .cm-templater-command.cm-def,
.cm-s-obsidian .cm-templater-command.cm-type.cm-def {
    color: var(--code-normal, var(--text-normal));
}

.cm-s-obsidian .cm-templater-command.cm-property,
.cm-s-obsidian .cm-templater-command.cm-property.cm-def,
.cm-s-obsidian .cm-templater-command.cm-attribute {
    color: var(--code-function, #98e342);
}

.cm-s-obsidian .cm-templater-command.cm-variable,
.cm-s-obsidian .cm-templater-command.cm-variable-2,
.cm-s-obsidian .cm-templater-command.cm-variable-3,
.cm-s-obsidian .cm-templater-command.cm-meta {
    color: var(--code-property, #d4d4d4);
}

.cm-s-obsidian .cm-templater-command.cm-callee,
.cm-s-obsidian .cm-templater-command.cm-operator,
.cm-s-obsidian .cm-templater-command.cm-qualifier,
.cm-s-obsidian .cm-templater-command.cm-builtin {
    color: var(--code-operator, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-tag {
    color: var(--code-tag, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-comment,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-tag,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-attribute {
    color: var(--code-comment, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-string,
.cm-s-obsidian .cm-templater-command.cm-string-2 {
    color: var(--code-string, #e6db74);
}

.cm-s-obsidian .cm-templater-command.cm-header,
.cm-s-obsidian .cm-templater-command.cm-hr {
    color: var(--code-keyword, #da7dae);
}

.cm-s-obsidian .cm-templater-command.cm-link {
    color: var(--code-normal, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-error {
    border-bottom: 1px solid #c42412;
}

.CodeMirror-hints {
    position: absolute;
    z-index: 10;
    overflow: hidden;
    list-style: none;

    margin: 0;
    padding: 2px;

    -webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 1px solid silver;

    background: white;
    font-size: 90%;
    font-family: monospace;

    max-height: 20em;
    overflow-y: auto;
}

.CodeMirror-hint {
    margin: 0;
    padding: 0 4px;
    border-radius: 2px;
    white-space: pre;
    color: black;
    cursor: pointer;
}

li.CodeMirror-hint-active {
    background: #08f;
    color: white;
}
 @charset "UTF-8";.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:rgba(0,0,0,.1)}.numInputWrapper span:active{background:rgba(0,0,0,.2)}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:rgba(0,0,0,.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}:root{--tasks-details-icon: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M8.59 16.58L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.42z'/></svg>")}ul.contains-task-list .task-list-item-checkbox{margin-inline-start:calc(var(--checkbox-size) * -1.5)!important}.plugin-tasks-query-explanation{--code-white-space: pre}.tasks-count{color:var(--text-faint);padding-left:20px}.tooltip.pop-up{animation:pop-up-animation .2s forwards ease-in-out}@keyframes pop-up-animation{0%{opacity:0;transform:translateY(-100%) scale(1)}20%{opacity:.7;transform:translateY(-100%) scale(1.02)}40%{opacity:1;transform:translateY(-100%) scale(1.05)}to{opacity:1;transform:translateY(-100%) scale(1)}}.task-cancelled,.task-created,.task-done,.task-due,.task-scheduled,.task-start{cursor:pointer;user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}.tasks-edit,.tasks-postpone{width:1em;height:1em;vertical-align:middle;margin-left:.33em;cursor:pointer;font-family:var(--font-interface);color:var(--text-accent);user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}a.tasks-edit,a.tasks-postpone{text-decoration:none}.tasks-edit:after{content:"\1f4dd"}.tasks-postpone:after{content:"\23e9"}.tasks-urgency{font-size:var(--font-ui-smaller);font-family:var(--font-interface);padding:2px 6px;border-radius:var(--radius-s);color:var(--text-normal);background-color:var(--background-secondary);margin-left:.5em;line-height:1}.internal-link.internal-link-short-mode{text-decoration:none}.tasks-list-text{position:relative}.tasks-list-text .tooltip{position:absolute;top:0;left:0;white-space:nowrap}.task-list-item-checkbox{cursor:pointer}.tasks-layout-hide-tags .task-description a.tag,.task-list-item .task-block-link{display:none}.tasks-modal section+section{margin-top:6px}.tasks-modal hr{margin:6px 0}.tasks-modal .tasks-modal-error{border:1px solid red!important}.tasks-modal .accesskey{text-decoration:underline;text-underline-offset:1pt}.tasks-modal-description-section textarea{width:100%;min-height:calc(var(--input-height) * 2);resize:vertical;margin-top:8px}.tasks-modal-priority-section{display:grid;grid-template-columns:6em auto auto auto;grid-row-gap:.15em}.tasks-modal-priority-section>label{grid-row-start:1;grid-row-end:3}.tasks-modal-priority-section .task-modal-priority-option-container{white-space:nowrap}.tasks-modal-priority-section .task-modal-priority-option-container input+label{font-size:var(--font-ui-small);border-radius:var(--input-radius);padding:2px 3px}.tasks-modal-priority-section .task-modal-priority-option-container input{accent-color:var(--interactive-accent)}.tasks-modal-priority-section .task-modal-priority-option-container input:focus+label{box-shadow:0 0 0 2px var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.tasks-modal-priority-section .task-modal-priority-option-container input:checked+label{font-weight:700}.tasks-modal-priority-section .task-modal-priority-option-container input:not(:checked)+label>span:nth-child(4){filter:grayscale(100%) opacity(60%)}.tasks-modal-dates-section{display:grid;grid-template-columns:6em 13em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dates-section label{grid-column:1}.tasks-modal-dates-section .tasks-modal-date-input{min-width:15em}.tasks-modal-dates-section .tasks-modal-date-editor-picker{margin-left:.5em}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:3;font-size:var(--font-ui-small)}.tasks-modal-dates-section .future-dates-only{grid-column-start:1;grid-column-end:3}.tasks-modal-dates-section .future-dates-only input{margin-left:.67em;top:2px}.tasks-modal-dates-section .status-editor-status-selector{grid-column:2}.tasks-modal-dependencies-section{display:grid;grid-template-columns:6em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dependencies-section .tasks-modal-dependency-input{grid-column:2;width:100%}.tasks-modal-dependencies-section .results-dependency{grid-column:2}.tasks-modal-button-section{position:sticky;bottom:0;background-color:var(--modal-background);padding-bottom:16px;padding-top:16px;display:grid;grid-template-columns:3fr 1fr;column-gap:.5em}.tasks-modal-button-section button:disabled{pointer-events:none!important;opacity:.3!important}@media (max-width: 649px){.tasks-modal-priority-section{grid-template-columns:6em auto auto}.tasks-modal-priority-section>label{grid-row:1/span 3}}@media (max-width: 499px){.tasks-modal-priority-section{grid-template-columns:4em auto auto}.tasks-modal-dates-section{grid-template-columns:1fr;grid-auto-columns:auto}.tasks-modal-dates-section .tasks-modal-date-input{grid-column:1}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:2}.tasks-modal-dates-section .status-editor-status-selector,.tasks-modal-dependencies-section label,.tasks-modal-dependencies-section .results-dependency{grid-column:1}}@media (max-width: 399px){.tasks-modal-dates-section .status-editor-status-selector{grid-column:1}.tasks-modal-dates-section>.tasks-modal-parsed-date{grid-column:1}.tasks-modal-priority-section{grid-template-columns:4em auto}.tasks-modal-priority-section>label{grid-row:1/span 6}.tasks-modal-dependencies-section{grid-template-columns:1fr;grid-auto-columns:auto}}@media (max-width: 259px){.tasks-modal-priority-section{grid-template-columns:1fr}.tasks-modal-priority-section>label{grid-row:1}}.task-dependencies-container{grid-column:2;display:flex;flex-wrap:wrap;gap:8px}.task-dependency{display:inline-flex;background-color:var(--interactive-normal);box-shadow:var(--input-shadow);border-radius:28px;padding:4px 4px 4px 8px}.task-dependency-name{font-size:var(--font-ui-small);max-width:160px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-delete{padding:3px;cursor:pointer;height:inherit;box-shadow:none!important;border-radius:50%}.task-dependency-dropdown{list-style:none;position:absolute;top:0;left:0;padding:4px;margin:0;background-color:var(--background-primary);border:1px;border-radius:6px;border-color:var(--background-modifier-border);border-style:solid;z-index:99;max-height:170px;overflow-y:auto}.task-dependency-dropdown li{padding:5px;margin:2px;border-radius:6px;cursor:pointer;display:flex;justify-content:space-between}.task-dependency-dropdown li .dependency-name{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-name-shared{width:60%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-path{width:40%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-style:italic;text-align:right;color:var(--italic-color)}.task-dependency-dropdown li.selected{background-color:var(--text-selection)}.tasks-setting-important{color:red;font-weight:700}.tasks-settings-is-invalid{color:var(--text-error)!important;background-color:rgba(var(--background-modifier-error-rgb),.2)!important}.tasks-settings .additional{margin:6px 12px}.tasks-settings .additional>.setting-item{border-top:0;padding-top:9px}.tasks-settings details>summary{outline:none;display:block!important;list-style:none!important;list-style-type:none!important;min-height:1rem;border-top-left-radius:.1rem;border-top-right-radius:.1rem;cursor:pointer;position:relative}.tasks-settings details>summary::-webkit-details-marker,.tasks-settings details>summary::marker{display:none!important}.tasks-settings details>summary>.collapser{position:absolute;top:50%;right:8px;transform:translateY(-50%);content:""}.tasks-settings details>summary>.collapser>.handle{transform:rotate(0);transition:transform .25s;background-color:currentColor;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:contain;mask-size:contain;-webkit-mask-image:var(--tasks-details-icon);mask-image:var(--tasks-details-icon);width:20px;height:20px}.tasks-settings details[open]>summary>.collapser>.handle{transform:rotate(90deg)}.tasks-nested-settings .setting-item{border:0px;padding-bottom:0}.tasks-nested-settings{padding-bottom:18px}.tasks-nested-settings[open] .setting-item-heading,.tasks-nested-settings:not(details) .setting-item-heading{border-top:0px;border-bottom:1px solid var(--background-modifier-border)}.tasks-settings .row-for-status{margin-top:0;margin-bottom:0}.tasks-settings .tasks-presets-wrapper{width:100%;position:relative;transition:all .2s ease}.tasks-settings .tasks-presets-wrapper.tasks-presets-dragging{opacity:.5;transform:rotate(2deg)}.tasks-settings .tasks-presets-wrapper.tasks-presets-drop-above:before{content:"";position:absolute;top:-2px;left:0;right:0;height:4px;background-color:var(--interactive-accent);border-radius:2px;z-index:10}.tasks-settings .tasks-presets-wrapper.tasks-presets-drop-below:after{content:"";position:absolute;bottom:-2px;left:0;right:0;height:4px;background-color:var(--interactive-accent);border-radius:2px;z-index:10}.tasks-settings .tasks-presets-setting .tasks-presets-key{grid-area:key}.tasks-settings .tasks-presets-setting .tasks-presets-key.has-error{border-color:var(--text-error);border-width:2px}.tasks-settings .tasks-presets-setting .tasks-presets-value{grid-area:value;min-width:300px;min-height:3em;font-family:var(--font-monospace);resize:horizontal;overflow-x:auto;overflow-y:hidden;white-space:pre}.tasks-settings .tasks-presets-setting .tasks-presets-drag-handle{grid-area:drag;color:var(--text-muted)}.tasks-settings .tasks-presets-setting .tasks-presets-drag-handle:hover{color:var(--text-normal)}.tasks-settings .tasks-presets-setting .tasks-presets-delete-button{grid-area:delete}.tasks-settings .tasks-presets-setting .setting-item-control{justify-content:start;display:grid;grid-template-columns:200px 1fr auto auto;grid-template-areas:"key value drag delete";gap:4px;align-items:unset;border:1px solid var(--background-modifier-border)!important;padding:.5em!important;background-color:var(--background-secondary)!important;border-radius:4px!important}@container (max-width: 600px){.tasks-settings .tasks-presets-setting .setting-item-control{grid-template-columns:5fr 1fr 1fr;grid-template-areas:"key drag delete" "value value value"}.tasks-settings .tasks-presets-setting .setting-item-control .tasks-presets-key{width:100%}}
 :root {
  --advanced-tables-helper-size: 28px;
}

.HyperMD-table-row span.cm-inline-code {
  font-size: 100%;
  padding: 0px;
}

.advanced-tables-buttons>div>.title {
  font-weight: var(--font-medium);
  font-size: var(--nav-item-size);
  color: var(--nav-item-color);
  text-decoration: underline;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container {
  column-gap: 0.2rem;
  margin: 0.2rem 0 0.2rem 0;
  justify-content: start;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container::before {
  min-width: 2.6rem;
  line-height: var(--advanced-tables-helper-size);
  font-size: var(--nav-item-size);
  font-weight: var(--nav-item-weight);
  color: var(--nav-item-color);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container>* {
  height: var(--advanced-tables-helper-size);
  line-height: var(--advanced-tables-helper-size);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button {
  width: var(--advanced-tables-helper-size);
  height: var(--advanced-tables-helper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-s);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button:hover {
  background-color: var(--nav-item-background-hover);
  color: var(--nav-item-color-hover);
  font-weight: var(--nav-item-weight-hover);
}

.advanced-tables-row-label {
  width: 50px;
}

.widget-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-muted);
}

.widget-icon:hover {
  fill: var(--text-normal);
}

.advanced-tables-csv-export textarea {
  height: 200px;
  width: 100%;
}

.advanced-tables-donation {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.advanced-tables-donate-button {
  margin: 10px;
} .choices{position:relative;margin-bottom:24px;font-size:16px}.choices:focus{outline:none}.choices:last-child{margin-bottom:0}.choices.is-disabled .choices__inner,.choices.is-disabled .choices__input{background-color:#eaeaea;cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none}.choices.is-disabled .choices__item{cursor:not-allowed}.choices [hidden]{display:none!important}.choices[data-type*=select-one]{cursor:pointer}.choices[data-type*=select-one] .choices__inner{padding-bottom:7.5px}.choices[data-type*=select-one] .choices__input{display:block;width:100%;padding:10px;border-bottom:1px solid #dddddd;background-color:#fff;margin:0}.choices[data-type*=select-one] .choices__button{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);padding:0;background-size:8px;position:absolute;top:50%;right:0;margin-top:-10px;margin-right:25px;height:20px;width:20px;border-radius:10em;opacity:.5}.choices[data-type*=select-one] .choices__button:hover,.choices[data-type*=select-one] .choices__button:focus{opacity:1}.choices[data-type*=select-one] .choices__button:focus{box-shadow:0 0 0 2px #00bcd4}.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button{display:none}.choices[data-type*=select-one]:after{content:"";height:0;width:0;border-style:solid;border-color:#333333 transparent transparent transparent;border-width:5px;position:absolute;right:11.5px;top:50%;margin-top:-2.5px;pointer-events:none}.choices[data-type*=select-one].is-open:after{border-color:transparent transparent #333333 transparent;margin-top:-7.5px}.choices[data-type*=select-one][dir=rtl]:after{left:11.5px;right:auto}.choices[data-type*=select-one][dir=rtl] .choices__button{right:auto;left:0;margin-left:25px;margin-right:0}.choices[data-type*=select-multiple] .choices__inner,.choices[data-type*=text] .choices__inner{cursor:text}.choices[data-type*=select-multiple] .choices__button,.choices[data-type*=text] .choices__button{position:relative;display:inline-block;margin:0 -4px 0 8px;padding-left:16px;border-left:1px solid #008fa1;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);background-size:8px;width:8px;line-height:1;opacity:.75;border-radius:0}.choices[data-type*=select-multiple] .choices__button:hover,.choices[data-type*=select-multiple] .choices__button:focus,.choices[data-type*=text] .choices__button:hover,.choices[data-type*=text] .choices__button:focus{opacity:1}.choices__inner{display:inline-block;vertical-align:top;width:100%;background-color:#f9f9f9;padding:7.5px 7.5px 3.75px;border:1px solid #dddddd;border-radius:2.5px;font-size:14px;min-height:44px;overflow:hidden}.is-focused .choices__inner,.is-open .choices__inner{border-color:#b7b7b7}.is-open .choices__inner{border-radius:2.5px 2.5px 0 0}.is-flipped.is-open .choices__inner{border-radius:0 0 2.5px 2.5px}.choices__list{margin:0;padding-left:0;list-style:none}.choices__list--single{display:inline-block;padding:4px 16px 4px 4px;width:100%}[dir=rtl] .choices__list--single{padding-right:4px;padding-left:16px}.choices__list--single .choices__item{width:100%}.choices__list--multiple{display:inline}.choices__list--multiple .choices__item{display:inline-block;vertical-align:middle;border-radius:20px;padding:4px 10px;font-size:12px;font-weight:500;margin-right:3.75px;margin-bottom:3.75px;background-color:#00bcd4;border:1px solid #00a5bb;color:#fff;word-break:break-all;box-sizing:border-box}.choices__list--multiple .choices__item[data-deletable]{padding-right:5px}[dir=rtl] .choices__list--multiple .choices__item{margin-right:0;margin-left:3.75px}.choices__list--multiple .choices__item.is-highlighted{background-color:#00a5bb;border:1px solid #008fa1}.is-disabled .choices__list--multiple .choices__item{background-color:#aaa;border:1px solid #919191}.choices__list--dropdown{visibility:hidden;z-index:1;position:absolute;width:100%;background-color:#fff;border:1px solid #dddddd;top:100%;margin-top:-1px;border-bottom-left-radius:2.5px;border-bottom-right-radius:2.5px;overflow:hidden;word-break:break-all;will-change:visibility}.choices__list--dropdown.is-active{visibility:visible}.is-open .choices__list--dropdown{border-color:#b7b7b7}.is-flipped .choices__list--dropdown{top:auto;bottom:100%;margin-top:0;margin-bottom:-1px;border-radius:.25rem .25rem 0 0}.choices__list--dropdown .choices__list{position:relative;max-height:300px;overflow:auto;-webkit-overflow-scrolling:touch;will-change:scroll-position}.choices__list--dropdown .choices__item{position:relative;padding:10px;font-size:14px}[dir=rtl] .choices__list--dropdown .choices__item{text-align:right}@media (min-width: 640px){.choices__list--dropdown .choices__item--selectable{padding-right:100px}.choices__list--dropdown .choices__item--selectable:after{content:attr(data-select-text);font-size:12px;opacity:0;position:absolute;right:10px;top:50%;transform:translateY(-50%)}[dir=rtl] .choices__list--dropdown .choices__item--selectable{text-align:right;padding-left:100px;padding-right:10px}[dir=rtl] .choices__list--dropdown .choices__item--selectable:after{right:auto;left:10px}}.choices__list--dropdown .choices__item--selectable.is-highlighted{background-color:#f2f2f2}.choices__list--dropdown .choices__item--selectable.is-highlighted:after{opacity:.5}.choices__item{cursor:default}.choices__item--selectable{cursor:pointer}.choices__item--disabled{cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none;opacity:.5}.choices__heading{font-weight:600;font-size:12px;padding:10px;border-bottom:1px solid #f7f7f7;color:gray}.choices__button{text-indent:-9999px;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:0;background-color:transparent;background-repeat:no-repeat;background-position:center;cursor:pointer}.choices__button:focus{outline:none}.choices__input{display:inline-block;vertical-align:baseline;background-color:#f9f9f9;font-size:14px;margin-bottom:5px;border:0;border-radius:0;max-width:100%;padding:4px 0 4px 2px}.choices__input:focus{outline:0}[dir=rtl] .choices__input{padding-right:2px;padding-left:0}.choices__placeholder{opacity:.5}.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:#0000001a}.numInputWrapper span:active{background:#0003}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:#0000000d}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:#0000000d}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:#0000000d}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0,0,0);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.workspace-leaf-content[data-type=kanban] .view-content{padding:0}.workspace-leaf-content[data-type=kanban]>.view-header{display:flex!important}.kanban-plugin{--lane-width: 272px}.kanban-plugin{contain:content;height:100%;width:100%;position:relative;display:flex;flex-direction:column}.kanban-plugin a.tag,.kanban-plugin__drag-container a.tag{padding-inline:var(--tag-padding-x);padding-block:var(--tag-padding-y)}.kanban-plugin__table-wrapper{height:100%;width:100%;overflow:auto;padding-block-end:40px;--table-column-first-border-width: 0;--table-column-last-border-width: 0;--table-row-last-border-width: 0}.kanban-plugin__table-wrapper table{width:fit-content;margin-block:0;margin-inline:auto;box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper tr{width:fit-content}.kanban-plugin__table-wrapper th,.kanban-plugin__table-wrapper td{text-align:start;vertical-align:top;font-size:.875rem;padding:0!important;height:1px}.kanban-plugin__table-wrapper th.mod-has-icon .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td.mod-has-icon .kanban-plugin__table-cell-wrapper{padding-inline-end:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td .kanban-plugin__table-cell-wrapper{height:100%;padding-inline:var(--size-4-2);padding-block:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__item-prefix-button-wrapper input[type=checkbox],.kanban-plugin__table-wrapper td .kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px}.kanban-plugin__table-wrapper th:has(.markdown-source-view),.kanban-plugin__table-wrapper td:has(.markdown-source-view){--background-primary: var(--background-primary-alt);background:var(--background-primary);outline:2px solid var(--background-modifier-border-focus)}.kanban-plugin__table-wrapper thead tr>th{height:1px;background-color:var(--background-primary);position:sticky;top:0;z-index:1;overflow:visible}.kanban-plugin__table-wrapper thead tr>th:nth-child(2n+2){background-color:var(--background-primary)}.kanban-plugin__table-wrapper thead tr>th .kanban-plugin__table-cell-wrapper{height:100%;padding-block:var(--size-2-2);padding-inline:var(--size-4-2) var(--size-2-2);box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper .resizer{position:absolute;top:0;height:100%;width:5px;background:var(--table-selection-border-color);cursor:col-resize;user-select:none;touch-action:none}.kanban-plugin__table-wrapper .resizer.ltr{right:0}.kanban-plugin__table-wrapper .resizer.rtl{left:0}.kanban-plugin__table-wrapper .resizer.isResizing{opacity:1}@media (hover: hover){.kanban-plugin__table-wrapper .resizer{opacity:0}.kanban-plugin__table-wrapper .resizer:hover{opacity:1}}.kanban-plugin__table-wrapper .kanban-plugin__item-tags:not(:empty){margin-block-start:-5px}.kanban-plugin__table-wrapper .kanban-plugin__item-metadata-date-relative{display:block}.kanban-plugin__table-wrapper .kanban-plugin__item-input-wrapper,.kanban-plugin__table-wrapper .cm-table-widget,.kanban-plugin__table-wrapper .kanban-plugin__item-title,.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper,.kanban-plugin__table-wrapper .kanban-plugin__item-content-wrapper{height:100%}.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper{padding:0}.kanban-plugin .markdown-source-view.mod-cm6{display:block;font-size:.875rem}.kanban-plugin .markdown-source-view.mod-cm6 .cm-scroller{overflow:visible}.kanban-plugin__table-header{display:flex;gap:var(--size-4-2);align-items:center;justify-content:space-between}.kanban-plugin__table-header-sort{line-height:1;color:var(--text-faint);padding:2px;border-radius:4px}.kanban-plugin__table-header-sort>span{display:flex}div:hover>.kanban-plugin__table-header-sort{background-color:var(--background-modifier-hover)}.kanban-plugin__cell-flex-wrapper{display:flex;gap:8px;align-items:flex-start;justify-content:space-between}.kanban-plugin__cell-flex-wrapper .lucide-more-vertical{transform:none}.kanban-plugin__icon-wrapper{display:flex;line-height:1}.kanban-plugin__icon-wrapper>.kanban-plugin__icon{display:flex}.kanban-plugin.something-is-dragging{cursor:grabbing;cursor:-webkit-grabbing}.kanban-plugin.something-is-dragging *{pointer-events:none}.kanban-plugin__item button,.kanban-plugin__lane button,.kanban-plugin button{line-height:1;margin:0;transition:.1s color,.1s background-color}.kanban-plugin__search-wrapper{width:100%;position:sticky;top:0;left:0;padding-block:10px;padding-inline:13px;display:flex;justify-content:flex-end;align-items:center;z-index:2;background-color:var(--background-primary)}button.kanban-plugin__search-cancel-button{display:flex;line-height:1;padding:6px;border:1px solid var(--background-modifier-border);background:var(--background-secondary-alt);color:var(--text-muted);margin-block:0;margin-inline:5px 0;font-size:16px}button.kanban-plugin__search-cancel-button .kanban-plugin__icon{display:flex}.kanban-plugin__icon{display:inline-block;line-height:1;--icon-size: 1em}.kanban-plugin__board{display:flex;width:100%;height:100%}.kanban-plugin__board>div{display:flex;align-items:flex-start;justify-content:flex-start;padding:1rem;width:fit-content;height:100%}.kanban-plugin__board.kanban-plugin__vertical>div{height:fit-content;width:100%;flex-direction:column}.is-mobile .view-content:not(.is-mobile-editing) .kanban-plugin__board>div{padding-bottom:calc(1rem + var(--mobile-navbar-height))}.kanban-plugin__board.is-adding-lane>div{padding-inline-end:calc(250px + 1rem)}.kanban-plugin__lane-wrapper{display:flex;flex-shrink:0;margin-inline-end:10px;max-height:100%;width:var(--lane-width)}.kanban-plugin__vertical .kanban-plugin__lane-wrapper{margin-block-end:10px;margin-inline-end:0}.kanban-plugin__lane{width:100%;display:flex;flex-direction:column;background-color:var(--background-secondary);border-radius:6px;border:1px solid var(--background-modifier-border)}.is-dropping>.kanban-plugin__lane{background-color:hsla(var(--interactive-accent-hsl),.15);border-color:hsla(var(--interactive-accent-hsl),1);outline:1px solid hsla(var(--interactive-accent-hsl),1)}.kanban-plugin__placeholder.kanban-plugin__lane-placeholder{height:100%;flex-grow:1;margin-inline-end:5px}.kanban-plugin__lane.is-hidden{display:none}.kanban-plugin__lane button{padding-block:8px;padding-inline:10px}.kanban-plugin__lane-form-wrapper{position:absolute;top:1rem;right:1rem;width:250px;background-color:var(--background-secondary);border-radius:6px;border:2px solid hsla(var(--interactive-accent-hsl),.7);z-index:var(--layer-popover);box-shadow:0 .5px 1px .5px #0000001a,0 2px 10px #0000001a,0 10px 20px #0000001a}.kanban-plugin__lane-input{--font-text-size: var(--font-ui-small);padding-block:var(--size-4-1);padding-inline:var(--size-4-2);background-color:var(--background-primary);border-radius:var(--radius-s)}.kanban-plugin__lane-input-wrapper{padding:10px}.kanban-plugin__item-input-actions,.kanban-plugin__lane-input-actions{display:flex;align-items:flex-start;justify-content:flex-start;padding-block:0 10px;padding-inline:10px}.kanban-plugin__item-input-actions button,.kanban-plugin__lane-input-actions button{display:block;margin-inline-end:5px}button.kanban-plugin__item-action-add,button.kanban-plugin__lane-action-add{background-color:var(--interactive-accent);color:var(--text-on-accent)}button.kanban-plugin__item-action-add:hover,button.kanban-plugin__lane-action-add:hover{background-color:var(--interactive-accent-hover)}.kanban-plugin__lane-header-wrapper{padding-block:8px;padding-inline:8px 12px;display:flex;align-items:center;gap:var(--size-4-1);flex-shrink:0;flex-grow:0;border-bottom:1px solid var(--background-modifier-border)}.collapse-horizontal .kanban-plugin__lane-header-wrapper,.collapse-vertical .kanban-plugin__lane-header-wrapper,.will-prepend .kanban-plugin__lane-header-wrapper{border-bottom:none}.kanban-plugin__lane-wrapper.collapse-horizontal{width:auto}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{writing-mode:vertical-lr}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{gap:var(--size-4-2)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-count,.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-text{transform:rotate(180deg)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-settings-button-wrapper{display:none}.kanban-plugin__lane-wrapper.collapse-vertical .kanban-plugin__lane-settings-button-wrapper{visibility:hidden}.kanban-plugin__lane-collapse{flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-collapse>span{display:flex}.collapse-vertical .kanban-plugin__lane-collapse>span{transform:rotate(-90deg)}.kanban-plugin__lane-grip{cursor:grab;flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-grip:active{cursor:grabbing}.kanban-plugin__lane-collapse svg{--icon-size: 1rem}.kanban-plugin__lane-grip>svg{height:1rem;display:block}.kanban-plugin__lane-title{font-weight:600;font-size:.875rem;flex-grow:1;width:100%;display:flex;flex-direction:column}.kanban-plugin__lane-title-text{flex-grow:1}div.kanban-plugin__lane-title-count{border-radius:3px;color:var(--text-muted);display:block;font-size:13px;line-height:1;padding:4px}div.kanban-plugin__lane-title-count.wip-exceeded{font-weight:700;color:var(--text-normal);background-color:rgba(var(--background-modifier-error-rgb),.25)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-postfix-button,.kanban-plugin__lane .kanban-plugin__lane-settings-button{--icon-stroke: 2.5px;font-size:13px;line-height:1;color:var(--text-muted);padding:4px;display:flex;margin-inline-end:-4px}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu.is-enabled,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__lane .kanban-plugin__lane-settings-button.is-enabled{color:var(--text-accent)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu{color:var(--text-faint);margin-inline-start:2px;margin-inline-end:0px}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button{margin-inline-end:4px;margin-inline-start:-4px}.kanban-plugin__table-cell-wrapper button.kanban-plugin__item-prefix-button,.kanban-plugin__item button.kanban-plugin__item-prefix-button{margin-block:4px;margin-inline:0 7px;padding:0}.kanban-plugin__lane-action-wrapper,.kanban-plugin__item-edit-archive-button,.kanban-plugin__item-settings-actions .kanban-plugin__icon,.kanban-plugin__item-edit-archive-button>.kanban-plugin__icon,.kanban-plugin__item-prefix-button>.kanban-plugin__icon,.kanban-plugin__item-postfix-button>.kanban-plugin__icon,.kanban-plugin__lane-settings-button>.kanban-plugin__icon{display:flex}.kanban-plugin__lane-settings-button-wrapper{display:flex;gap:4px}button.kanban-plugin__lane-settings-button+button.kanban-plugin__lane-settings-button{margin-inline-start:2px}.kanban-plugin__lane-settings-button svg{width:1em;height:1em}.kanban-plugin__lane-items-wrapper{margin:4px;height:100%}.kanban-plugin__lane-items{padding:4px;margin-block:0;margin-inline:4px;display:flex;flex-direction:column}.kanban-plugin__lane-items>div{margin-block-start:4px}.kanban-plugin__lane-items>.kanban-plugin__placeholder{flex-grow:1}.kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{height:2.55em;border:3px dashed rgba(var(--text-muted-rgb),.1);margin-block-end:4px;border-radius:6px;transition:border .2s ease}.is-sorting .kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{border-color:hsla(var(--interactive-accent-hsl),.6)}.kanban-plugin__item-button-wrapper{border-top:1px solid var(--background-modifier-border);padding:8px;flex-shrink:0;flex-grow:0}.kanban-plugin__item-button-wrapper>button{text-align:left;width:100%}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-button-wrapper{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-form{border-top:1px solid var(--background-modifier-border);padding:8px}.kanban-plugin__item-form .kanban-plugin__item-input-wrapper{padding-block:6px;padding-inline:8px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary);border-radius:var(--input-radius);min-height:var(--input-height)}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-form{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-input-wrapper{--line-height-normal: var(--line-height-tight);display:flex;flex-direction:column;flex-grow:1}.kanban-plugin button.kanban-plugin__item-submit-button{flex-grow:0;flex-shrink:1;font-size:14px;height:auto;line-height:1;margin-block-start:5px;width:auto}.is-mobile .kanban-plugin button.kanban-plugin__item-submit-button{font-size:12px}.is-mobile .kanban-plugin__lane-form-wrapper{--input-height: auto}.is-mobile .kanban-plugin__lane-form-wrapper button{padding-block:var(--size-4-2)}.is-mobile .kanban-plugin__lane-form-wrapper .markdown-source-view.mod-cm6{font-size:var(--font-ui-medium)}.is-mobile .kanban-plugin .kanban-plugin__lane-input-wrapper button.kanban-plugin__item-submit-button{display:none}button.kanban-plugin__new-item-button{background-color:transparent;color:var(--text-muted)}.kanban-plugin__new-item-button:hover{color:var(--text-on-accent);background-color:var(--interactive-accent-hover)}.kanban-plugin__drag-container>.kanban-plugin__item-wrapper .kanban-plugin__item{border-color:var(--interactive-accent);box-shadow:var(--shadow-s),0 0 0 2px hsla(var(--interactive-accent-hsl),.7)}.kanban-plugin__item{font-size:.875rem;border:1px solid var(--background-modifier-border);border-radius:var(--input-radius);overflow:hidden;transition:.3s opacity cubic-bezier(.25,1,.5,1)}.kanban-plugin__item:has(.markdown-source-view){outline:1px solid var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.kanban-plugin__item-content-wrapper{background:var(--background-primary)}.kanban-plugin__item-title-wrapper{background:var(--background-primary);display:flex;padding-block:6px;padding-inline:8px}.kanban-plugin__item-title-wrapper:not(:only-child){border-bottom:1px solid var(--background-modifier-border)}.kanban-plugin__item-title{width:100%;line-height:var(--line-height-tight);margin-block-start:1px}.kanban-plugin__meta-value,.kanban-plugin__markdown-preview-wrapper{white-space:pre-wrap;white-space:break-spaces;word-break:break-word;overflow-wrap:anywhere;--font-text-size: .875rem;--line-height-normal: var(--line-height-tight);--p-spacing: var(--size-4-2);--list-indent: 1.75em}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{--file-margins: 0}.kanban-plugin__meta-value.inline,.kanban-plugin__markdown-preview-wrapper.inline{display:inline-block}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:first-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:last-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:last-child{margin-block-end:0}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{width:unset;height:unset;position:unset;overflow-y:unset;overflow-wrap:unset;color:unset;user-select:unset;-webkit-user-select:unset;white-space:normal}.kanban-plugin__meta-value .markdown-preview-view .markdown-embed,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view .markdown-embed,.kanban-plugin__meta-value .markdown-preview-view blockquote,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view blockquote{padding-inline:var(--size-4-2) 0;padding-block:var(--size-4-1);margin-block-start:var(--p-spacing);margin-block-end:var(--p-spacing)}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view{display:inline-flex}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:first-child>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:last-child>*:last-child{margin-block-end:0}.kanban-plugin__embed-link-wrapper{padding:2px;float:right}.kanban-plugin__item-metadata-wrapper:not(:empty){background-color:var(--background-primary-alt);padding-inline:8px;padding-block:6px}.kanban-plugin__item-metadata:not(:empty){padding-block-start:5px;font-size:12px}.kanban-plugin__item-metadata:not(:empty) .markdown-preview-view{line-height:var(--line-height-tight);font-size:inherit}.kanban-plugin__item-metadata>span{display:block}.kanban-plugin__item-metadata>span.kanban-plugin__item-metadata-date-wrapper{display:inline-block}.kanban-plugin__item-metadata .is-button{cursor:var(--cursor)}.kanban-plugin__item-metadata .is-button:hover{color:var(--text-normal)}.kanban-plugin__item-metadata-date-relative:first-letter{text-transform:uppercase}.kanban-plugin__item-metadata a{text-decoration:none}.kanban-plugin__item-task-inline-metadata-item,.kanban-plugin__item-task-metadata-item{display:inline-flex;margin-block:3px 0;margin-inline:0 6px;gap:4px}.kanban-plugin__item-task-inline-metadata-item{padding-inline:2px;background-color:var(--background-secondary);border-radius:var(--radius-s)}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-task-inline-metadata-item{background-color:unset;padding-inline:unset;border-radius:unset}.kanban-plugin__item-tags:not(:empty){padding-block-start:2px}.kanban-plugin__item-tag{display:inline-block;margin-inline-end:4px}.kanban-plugin__item-tags .kanban-plugin__item-tag{font-size:12px;background-color:var(--tag-background, hsla(var(--interactive-accent-hsl), .1));color:var(--tag-color, var(--text-accent));margin-block:3px 0;margin-inline:0 3px}.kanban-plugin__item-tag.is-search-match,.kanban-plugin__item-tags .kanban-plugin__item-tag.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-table{width:100%;margin:0;line-height:var(--line-height-tight);font-size:.75rem}.kanban-plugin__meta-table .markdown-preview-view{font-size:.75rem}.kanban-plugin__meta-table .kanban-plugin__item-tags .kanban-plugin__item-tag{position:relative;inset-block-start:-2px;margin-block:0 3px}.kanban-plugin__meta-table td{vertical-align:top;padding-block:3px 0;padding-inline:0;width:10%}.kanban-plugin__meta-table td+td{width:90%}.kanban-plugin__meta-table td:only-child{width:100%}.kanban-plugin__meta-table td.kanban-plugin__meta-key{white-space:nowrap;padding-inline-end:5px;color:var(--text-muted)}.kanban-plugin__meta-table td.kanban-plugin__meta-key.is-search-match>span{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-value:not(.mod-array){white-space:pre-wrap;display:flex}.kanban-plugin__meta-value>.is-search-match,.kanban-plugin__meta-value.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__item-prefix-button-wrapper,.kanban-plugin__item-postfix-button-wrapper{display:flex;flex-grow:0;flex-shrink:0;align-self:start}.kanban-plugin__item-prefix-button-wrapper>div,.kanban-plugin__item-postfix-button-wrapper>div{display:flex;flex-direction:column;gap:var(--size-4-1)}.kanban-plugin__item-prefix-button-wrapper{flex-direction:column}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button{width:var(--checkbox-size);height:var(--checkbox-size)}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px;margin-inline:0px 7px}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button+button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]+button{margin-block-start:10px}button.kanban-plugin__item-postfix-button{visibility:hidden;opacity:0;transition:.1s opacity;display:flex;align-self:flex-start}button.kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__item:hover button.kanban-plugin__item-postfix-button{visibility:visible;opacity:1}.kanban-plugin__item-settings-actions{padding:5px;display:flex}.kanban-plugin__item-settings-actions>button{line-height:1;display:flex;align-items:center;justify-content:center;font-size:.75rem;width:100%}.kanban-plugin__lane-action-wrapper button>.kanban-plugin__icon,.kanban-plugin__item-settings-actions button>.kanban-plugin__icon{margin-inline-end:5px}.kanban-plugin__item-settings-actions>button:first-child,.kanban-plugin__lane-action-wrapper>button:first-child{margin-inline-end:2.5px}.kanban-plugin__item-settings-actions>button:last-child,.kanban-plugin__lane-action-wrapper>button:last-child{margin-inline-start:2.5px}.kanban-plugin__archive-lane-button,.kanban-plugin__item-button-archive{color:var(--text-muted);border:1px solid var(--background-modifier-border)}.kanban-plugin__archive-lane-button:hover,.kanban-plugin__item-button-archive:hover{color:var(--text-normal)}.kanban-plugin__item-button-delete{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__checkbox-wrapper{border-top:1px solid var(--background-modifier-border);border-bottom:1px solid var(--background-modifier-border);padding:10px;margin-block-end:10px;display:flex;align-items:center}.kanban-plugin__checkbox-wrapper .checkbox-container{flex-shrink:0;flex-grow:0;margin-inline-start:15px}.kanban-plugin__checkbox-label{font-size:.8125rem;line-height:var(--line-height-tight)}.kanban-plugin__lane-setting-wrapper>div{border-top:none;border-bottom:none;padding-block:10px;padding-inline:15px;margin-block-end:0}.kanban-plugin__lane-setting-wrapper>div:last-child{border-bottom:1px solid var(--background-modifier-border);margin-block-end:10px}.kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),.2);background-color:rgba(var(--background-modifier-error-rgb),.1);border-radius:4px;padding:10px;margin-block:5px;margin-inline:10px}.theme-dark .kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button,.kanban-plugin__archive-lane-button{display:flex;align-items:center;justify-content:center;font-size:.75rem;width:50%}.kanban-plugin__delete-lane-button{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__action-confirm-text{font-size:.875rem;color:var(--text-error);margin-block-end:10px;line-height:var(--line-height-tight)}button.kanban-plugin__confirm-action-button{border:1px solid rgba(var(--background-modifier-error-rgb),.2);margin-inline-end:5px;color:var(--text-error)}button.kanban-plugin__confirm-action-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.5)}button.kanban-plugin__cancel-action-button{border:1px solid var(--background-modifier-border)}.modal.kanban-plugin__board-settings-modal{width:var(--modal-width);height:var(--modal-height);max-height:var(--modal-max-height);max-width:var(--modal-max-width);padding:0;display:flex;flex-direction:column}.modal.kanban-plugin__board-settings-modal .modal-content{padding-block:30px;padding-inline:50px;height:100%;overflow-y:auto;overflow-x:hidden;margin:0}.kanban-plugin__board-settings-modal .setting-item{flex-wrap:wrap;justify-content:space-between}.kanban-plugin__board-settings-modal .setting-item-info{max-width:400px;min-width:300px;width:50%}.kanban-plugin__board-settings-modal .setting-item-control{min-width:300px;flex-shrink:0}.kanban-plugin__board-settings-modal .choices{width:100%;text-align:left}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__inner{background-color:var(--background-primary);border-color:var(--background-modifier-border);padding:0;min-height:0}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__input{background-color:var(--background-primary);border-bottom-color:var(--background-modifier-border);font-size:14px}.kanban-plugin__board-settings-modal .choices__input{border-radius:0;border-top:none;border-left:none;border-right:none}.kanban-plugin__board-settings-modal .choices__list[role=listbox]{overflow-x:hidden}.kanban-plugin__board-settings-modal .choices__list--single{padding-block:4px;padding-inline:6px 20px}.kanban-plugin__board-settings-modal .is-open .choices__list--dropdown,.kanban-plugin__board-settings-modal .choices__list--dropdown{background-color:var(--background-primary);border-color:var(--background-modifier-border);word-break:normal;max-height:200px;display:flex;flex-direction:column}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable:after{display:none}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable{padding-block:4px;padding-inline:6px}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item.is-highlighted{background-color:var(--background-primary-alt)}.kanban-plugin__board-settings-modal .choices__placeholder{opacity:1;color:var(--text-muted)}.kanban-plugin__board-settings-modal .error{border-color:var(--background-modifier-error-hover)!important}.kanban-plugin__date-picker{position:absolute;z-index:var(--layer-popover);--cell-size: 2.4em}.kanban-plugin__date-picker .flatpickr-input{width:0;height:0;opacity:0;border:none;padding:0;display:block;margin-block-end:-1px}.kanban-plugin__date-picker .flatpickr-current-month{color:var(--text-normal);font-weight:600;font-size:inherit;width:100%;position:static;height:auto;display:flex;align-items:center;justify-content:center;padding:0}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:var(--text-normal)}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{fill:currentColor}.kanban-plugin__date-picker .flatpickr-calendar{border-radius:var(--radius-m);font-size:13px;overflow:hidden;background-color:var(--background-primary);width:calc(var(--cell-size) * 7 + 8px);box-shadow:0 0 0 1px var(--background-modifier-border),0 15px 25px #0003}.kanban-plugin__date-picker .flatpickr-calendar.inline{top:0}.kanban-plugin__date-picker .flatpickr-months{font-size:13px;padding-block:2px 4px;padding-inline:2px;align-items:center}.kanban-plugin__date-picker .flatpickr-months .flatpickr-current-month input.cur-year,.kanban-plugin__date-picker .flatpickr-months select{border-radius:4px;padding:4px}.kanban-plugin__date-picker .flatpickr-months .numInputWrapper{border-radius:4px}.kanban-plugin__date-picker .flatpickr-months .flatpickr-month{width:100%;height:auto}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month{color:var(--text-normal);fill:currentColor;border-radius:4px;display:flex;align-items:center;justify-content:center;line-height:1;height:auto;padding:5px;position:static;flex-shrink:0}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover{background-color:var(--background-primary-alt);color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover svg,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover svg{fill:currentColor}.kanban-plugin__date-picker .flatpickr-current-month .flatpickr-monthDropdown-months{box-shadow:none;color:var(--text-normal);font-weight:inherit;margin-inline-end:5px}.kanban-plugin__date-picker .flatpickr-current-month input.cur-year{color:var(--text-normal);font-weight:inherit}.kanban-plugin__date-picker .flatpickr-weekdays{height:auto;padding-block:8px 12px;padding-inline:0}.kanban-plugin__date-picker span.flatpickr-weekday{font-weight:400;color:var(--text-muted)}.kanban-plugin__date-picker .flatpickr-innerContainer{padding:4px}.kanban-plugin__date-picker .flatpickr-day{color:var(--text-normal);display:inline-flex;align-items:center;justify-content:center;width:var(--cell-size);height:var(--cell-size);line-height:1;border-radius:6px}.kanban-plugin__date-picker .flatpickr-day.today{border-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-day.today:hover{color:var(--text-normal);border-color:var(--interactive-accent);background-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.selected{border-color:var(--interactive-accent);background-color:var(--interactive-accent);color:var(--text-on-accent)}.kanban-plugin__date-picker .flatpickr-day.selected:hover{border-color:var(--interactive-accent);background-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-days{width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .dayContainer{width:calc(var(--cell-size) * 7);min-width:calc(var(--cell-size) * 7);max-width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .flatpickr-day.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.today.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day:focus,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:focus,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:focus{background-color:var(--background-primary-alt);border-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled,.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed,.kanban-plugin__date-picker .flatpickr-day.notAllowed.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed.nextMonthDay{color:var(--text-faint)}.kanban-plugin__time-picker{position:absolute;max-height:250px;overflow:auto;border-radius:4px;border:1px solid var(--background-modifier-border);box-shadow:0 2px 8px var(--background-modifier-box-shadow);background:var(--background-primary);color:var(--text-normal);font-size:14px;z-index:var(--layer-menu)}.kanban-plugin__time-picker-item{display:flex;align-items:center;color:var(--text-muted);cursor:var(--cursor);line-height:1;padding-block:6px;padding-inline:8px}.kanban-plugin__time-picker-check{visibility:hidden;display:inline-flex;margin-inline-end:5px}.kanban-plugin__time-picker-item.is-hour{color:var(--text-normal);font-weight:600}.kanban-plugin__time-picker-item.is-selected .kanban-plugin__time-picker-check{visibility:visible}.kanban-plugin__time-picker-item:hover,.kanban-plugin__time-picker-item.is-selected{background:var(--background-secondary)}.kanban-plugin mark{background-color:var(--text-highlight-bg)}.kanban-plugin__draggable-setting-container{border-top:0;padding:0;flex-direction:column}.kanban-plugin__draggable-setting-container>div{width:100%;margin-inline-end:0!important}.kanban-plugin__setting-item-wrapper{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__draggable-setting-container>.kanban-plugin__placeholder{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__setting-item{background-color:var(--background-secondary);width:100%;font-size:16px;display:flex;align-items:flex-start;padding:12px;color:var(--text-muted)}.kanban-plugin__drag-container .kanban-plugin__setting-item{border:1px solid hsla(var(--interactive-accent-hsl),.8);box-shadow:0 15px 25px #0003,0 0 0 2px hsla(var(--interactive-accent-hsl),.8)}.kanban-plugin__setting-controls-wrapper{flex-grow:1;flex-shrink:1}.kanban-plugin__setting-input-wrapper{display:flex;flex-wrap:wrap;margin-block-end:1rem}.kanban-plugin__setting-input-wrapper>div{margin-inline-end:10px}.kanban-plugin__setting-toggle-wrapper>div{display:flex;align-items:center;line-height:1;margin-block-end:10px}.kanban-plugin__setting-toggle-wrapper .checkbox-container{margin-inline-end:10px}.kanban-plugin__setting-button-wrapper{display:flex;justify-content:flex-end;flex-grow:1;flex-shrink:0;max-width:25px}.kanban-plugin__setting-button-wrapper>div{margin-inline-start:12px}.kanban-plugin__setting-key-input-wrapper{margin-block:1rem;margin-inline:0}.kanban-plugin__setting-key-input-wrapper>input{margin-inline-end:10px}.kanban-plugin__date-color-input-wrapper,.kanban-plugin__tag-sort-input-wrapper,.kanban-plugin__tag-color-input-wrapper{display:flex;flex-direction:column;flex-grow:1;gap:1rem}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-key-input-wrapper{margin-block-start:0}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-input-wrapper{margin:0}.kanban-plugin__add-tag-color-button{align-self:baseline;margin:0}.kanban-plugin__date-color-wrapper,.kanban-plugin__tag-color-input .kanban-plugin__item-tags{background-color:var(--background-primary);padding:10px;margin:0;border-radius:4px}.kanban-plugin__tag-color-input .kanban-plugin__item-tag{margin-block-start:0;font-size:13px;font-weight:500;line-height:1.5}.kanban-plugin__date-color-input-wrapper input[type=number]{width:75px;padding-block:.6em;padding-inline:.8em;height:auto;border-radius:.5em}.kanban-plugin__date-color-input-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__date-color-config{padding-block:0 10px;padding-inline:0;display:flex;flex-wrap:wrap;gap:5px;align-items:center}.kanban-plugin__date-color-wrapper{display:inline-block;margin-block-start:10px}.kanban-plugin__date-color-wrapper .kanban-plugin__item-metadata{padding:0}.kanban-plugin__metadata-setting-desc{font-size:14px}.kanban-plugin__setting-button-spacer{visibility:hidden}.kanban-plugin__setting-item-label{font-size:12px;font-weight:700;margin-block-end:5px}.kanban-plugin__setting-toggle-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__hitbox{border:2px dashed tomato}.kanban-plugin__placeholder{flex-grow:0;flex-shrink:0;width:0;height:0;pointer-events:none}.kanban-plugin__placeholder[data-axis=horizontal]{height:100%}.kanban-plugin__placeholder[data-axis=vertical]{width:100%}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar{background-color:transparent;width:16px;height:16px}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar-thumb{border:4px solid transparent;background-clip:content-box}.kanban-plugin__scroll-container{will-change:transform}.kanban-plugin__scroll-container.kanban-plugin__horizontal{overflow-y:hidden;overflow-x:auto}.kanban-plugin__scroll-container.kanban-plugin__vertical{overflow-y:auto;overflow-x:hidden}.kanban-plugin__drag-container{contain:layout size;z-index:10000;pointer-events:none;position:fixed;top:0;left:0}.kanban-plugin__loading{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.sk-pulse{width:60px;height:60px;background-color:var(--text-faint);border-radius:100%;animation:sk-pulse 1.2s infinite cubic-bezier(.455,.03,.515,.955)}@keyframes sk-pulse{0%{transform:scale(0)}to{transform:scale(1);opacity:0}}.kanban-plugin__color-picker-wrapper{position:relative}.kanban-plugin__color-picker{position:absolute;top:-5px;left:0;transform:translateY(-100%)}.kanban-plugin__date,.cm-kanban-time-wrapper,.cm-kanban-date-wrapper{display:inline-block;color:var(--date-color);border-radius:var(--radius-s);background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}.kanban-plugin__date:hover,.cm-kanban-time-wrapper:hover,.cm-kanban-date-wrapper:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .1))}.kanban-plugin__date.kanban-plugin__preview-date-link,.cm-kanban-time-wrapper.kanban-plugin__preview-date-link,.cm-kanban-date-wrapper.kanban-plugin__preview-date-link{--link-decoration: none;--link-unresolved-decoration-style: unset}.kanban-plugin__date>span,.cm-kanban-time-wrapper>span,.cm-kanban-date-wrapper>span,.kanban-plugin__date>a,.cm-kanban-time-wrapper>a,.cm-kanban-date-wrapper>a{padding-inline:var(--size-2-1)}.completion .kanban-plugin__date.has-background{color:inherit;background-color:transparent}.completion .kanban-plugin__date.has-background:hover{background-color:transparent}.is-date .kanban-plugin__date:not(.has-background){background-color:transparent}.is-date .kanban-plugin__date:not(.has-background):hover{background-color:transparent}.kanban-plugin__meta-value .kanban-plugin__date:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}
 .configureMacroDiv{display:grid;grid-template-rows:1fr;min-width:12rem}.configureMacroDivItem{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.configureMacroDivItemButton{display:flex;align-content:center;justify-content:center;margin-bottom:10px}.macroContainer{display:grid;grid-template-rows:repeat(auto-fill,120px);grid-gap:40px;overflow-y:auto;max-height:30em;padding:2em}@media screen and (max-width: 540px){.macroContainer1,.macroContainer2,.macroContainer3{grid-template-columns:repeat(1,1fr)}.wideInputPromptInputEl{width:20rem;max-width:100%;height:3rem;direction:inherit;text-align:inherit}}@media screen and (max-width: 540px) and (max-width: 780px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:30rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}@media screen and (min-width: 781px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:40rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}.addMacroBarContainer{display:flex;align-content:center;justify-content:space-around;margin-top:20px}.captureToActiveFileContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.choiceNameHeader{text-align:center}.choiceNameHeader:hover{cursor:pointer}.folderInputContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:8px;gap:4px}.selectMacroDropdownContainer{display:flex;align-content:center;justify-content:center}.quickAddModal .modal{min-width:35%;overflow-y:auto;max-height:70%}.checkboxRowContainer{margin:30px 0;display:grid;grid-template-rows:auto;align-content:center;gap:5px}.checkboxRow{display:flex;justify-content:space-between;align-content:center}.checkboxRow .checkbox-container{flex-shrink:0}.checkboxRow span{font-size:16px;word-break:break-all}.submitButtonContainer{display:flex;align-content:center;justify-content:center}.chooseFolderWhenCreatingNoteContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.chooseFolderFromSubfolderContainer{margin:20px 0 0}.clickable:hover{cursor:pointer}.quickAddCommandListItem{display:flex;flex:1 1 auto;align-items:center;justify-content:space-between}.quickCommandContainer{display:flex;justify-content:flex-end;align-content:center;margin-bottom:1em;gap:4px}.yesNoPromptButtonContainer{display:flex;align-items:center;justify-content:space-around;margin-top:2rem}.yesNoPromptParagraph{text-align:center}.suggestion-container{background-color:var(--modal-background);z-index:100000;overflow-y:auto}.qaFileSuggestionItem{display:flex;align-items:center;justify-content:space-between;padding:6px 12px;margin:1px 0;cursor:pointer;border-radius:3px;transition:all .12s cubic-bezier(.25,.46,.45,.94);position:relative;min-height:32px;font-size:13px;line-height:1.3;width:100%}.qaFileSuggestionItem:hover{background-color:var(--background-modifier-hover);transform:translate(2px)}.qaFileSuggestionItem.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent);box-shadow:0 2px 8px #0000001a}.qaFileSuggestionItem.is-selected .suggestion-sub-text{color:var(--text-on-accent-inverted);opacity:.8}.qa-suggest-exact:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--interactive-accent);border-radius:0 1px 1px 0}.qa-suggest-alias:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-orange, #ff8c00);border-radius:0 1px 1px 0}.qa-suggest-fuzzy:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--text-faint);border-radius:0 1px 1px 0}.qa-suggest-unresolved:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-red, #e74c3c);border-radius:0 1px 1px 0}.qa-suggest-heading:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-blue, #3498db);border-radius:0 1px 1px 0}.qa-suggest-block:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-purple, #9b59b6);border-radius:0 1px 1px 0}.qa-suggestion-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0}.qaFileSuggestionItem .suggestion-main-text{font-weight:500;color:var(--text-normal);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:200px}.qaFileSuggestionItem .suggestion-sub-text{font-size:11px;color:var(--text-muted);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:150px;margin-left:auto;padding-left:8px;opacity:.7}.qa-suggestion-pill{display:inline-block;padding:1px 4px;border-radius:2px;font-size:9px;font-weight:600;text-transform:uppercase;letter-spacing:.3px;line-height:1;opacity:.8}.qa-alias-pill{background:var(--color-orange, #ff8c00);color:#fff}.qa-unresolved-pill{background:var(--color-red, #e74c3c);color:#fff}.qa-create-pill{background:var(--color-green, #27ae60);color:#fff}.qa-heading-pill{background:var(--color-blue, #3498db);color:#fff}.qa-block-pill{background:var(--color-purple, #9b59b6);color:#fff}.qa-file-tooltip{background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;padding:8px 10px;max-width:280px;box-shadow:0 4px 20px #0000001f;z-index:1000;pointer-events:none;font-size:12px}.qa-tooltip-header{font-weight:600;font-size:13px;margin-bottom:4px;color:var(--text-normal)}.qa-tooltip-content{color:var(--text-muted);line-height:1.3}.qa-tooltip-content div{margin-bottom:2px}.qaFileSuggestionItem{will-change:transform,background-color;backface-visibility:hidden}.qaFileSuggestionItem:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:-2px}.theme-dark .qa-file-tooltip{box-shadow:0 4px 20px #0000004d;border-color:var(--background-modifier-border-hover)}.theme-dark .qaFileSuggestionItem:hover{background-color:#ffffff0d}@media (prefers-contrast: high){.qa-suggest-exact:before,.qa-suggest-alias:before,.qa-suggest-fuzzy:before,.qa-suggest-unresolved:before,.qa-suggest-heading:before,.qa-suggest-block:before{width:3px}.qa-suggestion-pill{font-weight:700}}@media (prefers-reduced-motion: reduce){.qaFileSuggestionItem{transition:none}.qaFileSuggestionItem:hover{transform:none}}.choiceListItem{display:flex;font-size:16px;align-items:center;margin:12px 0 0;transition:1s ease-in-out}.choiceListItemName{flex:1 0 0}.choiceListItemName p{margin:0;display:inline}.quickadd-choice-suggestion p{margin:0}.macroDropdownContainer{display:flex;align-content:center;justify-content:center;margin-bottom:10px;gap:10px}.macro-choice-buttonsContainer{display:flex;flex-direction:row;justify-content:center;align-items:center;gap:10px}@media only screen and (max-width: 600px){.macroDropdownContainer{flex-direction:column;align-items:center}.macroDropdownContainer .macro-choice-buttonsContainer{gap:20px}}.quickadd-update-modal-container{display:flex;flex-direction:column;align-items:center;justify-content:center}.quickadd-update-modal{min-width:35%;max-width:90%;max-height:70%;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;overflow-x:hidden;overflow-y:auto}.quickadd-update-modal pre{white-space:pre-wrap;word-wrap:break-word;overflow-x:auto;max-width:100%}.quickadd-update-modal code{word-wrap:break-word;white-space:pre-wrap;max-width:100%;overflow-x:auto}.quickadd-update-modal img{width:100%;height:auto;margin:5px}.quickadd-bmac-container{display:flex;justify-content:center;align-items:center}
 .omnisearch-modal {
}

.omnisearch-result {
  white-space: normal;
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  flex-wrap: nowrap;
}

.omnisearch-result__title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 5px;
  flex-wrap: wrap;
}

.omnisearch-result__title {
  white-space: pre-wrap;
  align-items: center;
  display: flex;
  gap: 5px;
}

.omnisearch-result__title > span {
}

.omnisearch-result__folder-path {
  font-size: 0.75rem;
  align-items: center;
  display: flex;
  gap: 5px;
  color: var(--text-muted);
}

.omnisearch-result__extension {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__counter {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__body {
  white-space: normal;
  font-size: small;
  word-wrap: normal;

  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;

  color: var(--text-muted);
  margin-inline-start: 0.5em;
}

.omnisearch-result__embed {
  margin-left: 1em;
}


.omnisearch-result__image-container {
  flex-basis: 20%;
  text-align: end;
}

.omnisearch-highlight {
}

.omnisearch-default-highlight {
  text-decoration: underline;
  text-decoration-color: var(--text-highlight-bg);
  text-decoration-thickness: 3px;
  text-underline-offset: -1px;
  text-decoration-skip-ink: none;
}

.omnisearch-input-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.omnisearch-result__icon {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.omnisearch-result__icon svg {
  width: 100%;
  height: 100%;
}

.omnisearch-result__icon--emoji {
  font-size: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

@media only screen and (max-width: 600px) {
  .omnisearch-input-container {
    flex-direction: column;
  }

  .omnisearch-input-container__buttons {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0 1em 0 1em;
    gap: 1em;
  }
  .omnisearch-input-container__buttons > button {
    flex-grow: 1;
  }
}

@media only screen and (min-width: 600px) {
  .omnisearch-input-container__buttons {
    margin-inline-end: 1em;
  }
}

.omnisearch-input-field {
  position: relative;
  flex-grow: 1;
}
 .modal.mod-importer {
	max-height: var(--modal-height);
	padding: var(--size-4-4) 0 0 0;
	position: relative;
	overflow: hidden;
}
.modal.mod-importer .modal-title {
	padding: 0 var(--size-4-4);
}
.modal.mod-importer .modal-content {
	overflow: auto;
	padding: var(--size-4-4);
	margin-bottom: calc(var(--input-height) + var(--size-4-8));
	border-top: var(--border-width) solid var(--background-modifier-border);
}
.modal.mod-importer .modal-button-container {
	margin: 0 0 0 calc(var(--size-4-4) * -1);
	padding: var(--size-4-4);
	gap: var(--size-4-2);
	position: absolute;
	bottom: 0;
	background-color: var(--background-primary);
	border-top: var(--border-width) solid var(--background-modifier-border);
	width: 100%;
}

.importer-progress-bar {
	width: 100%;
	height: 8px;
	background-color: var(--background-secondary);
	overflow: hidden;
	box-shadow: inset 0px 0px 0px 1px var(--background-modifier-border);
	border-radius: var(--radius-s);
}

.importer-progress-bar-inner {
	width: 0;
	height: 100%;
	background-color: var(--interactive-accent);
}

.importer-status {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: var(--size-4-2) 0;
}

.importer-stats-container {
	display: flex;
	justify-content: space-evenly;
	margin-top: var(--size-4-5);
	margin-bottom: var(--size-4-5);
}

.importer-stat {
	text-align: center;
}

.importer-stat-count {
	font-size: var(--font-ui-large);
}

.importer-log {
	overflow: auto;
	flex-grow: 1;
	font-family: var(--font-monospace);
	font-size: var(--font-ui-smaller);
	color: var(--text-muted);
	border: 1px solid var(--background-modifier-border);
	padding: var(--size-4-4);
	background-color: var(--background-secondary);
	border-radius: var(--radius-s);
	max-height: 300px;
	user-select: text;
}

.importer-log .list-item {
	display: inline-block;
	line-height: var(--line-height-normal);
	white-space: pre;
	margin: var(--size-2-1);
}

.importer-error {
	color: var(--text-error);
}
 .linter-navigation-item{align-items:center;background-color:var(--background-primary-secondary-alt);border:1px solid var(--background-modifier-border);border-radius:100px;border-radius:8px 8px 2px 2px;cursor:pointer;display:flex;flex-direction:row;font-size:16px;font-weight:700;gap:4px;height:32px;overflow:hidden;padding:4px 6px;transition:color .25s ease-in-out,padding .25s ease-in-out,background-color .35s cubic-bezier(.45,.25,.83,.67),max-width .35s cubic-bezier(.57,.04,.58,1);white-space:nowrap}@media screen and (max-width:1325px){.linter-navigation-item.linter-desktop{max-width:32px}}@media screen and (max-width:800px){.linter-navigation-item.linter-mobile{max-width:32px}}.linter-navigation-item-icon,.linter-warning{padding-top:5px}.linter-navigation-item:hover{border-color:var(--interactive-accent-hover);border-bottom:0}.linter-navigation-item-selected{background-color:var(--interactive-accent)!important;border:1px solid var(--background-modifier-border);border-bottom:0;border-radius:8px 8px 2px 2px;color:var(--text-on-accent);max-width:100%!important;padding:4px 9px!important;transition:color .25s ease-in-out,padding .25s ease-in-out,background-color .35s cubic-bezier(.45,.25,.83,.67),max-width .45s cubic-bezier(.57,.04,.58,1) .2s}.linter{transition:transform .4s 0s}.linter-setting-title{align-items:baseline;display:flex;gap:30px;justify-content:space-between}.linter-setting-title.linter-mobile{justify-content:space-around}.linter-setting-title h1{font-weight:900;margin-bottom:12px;margin-top:6px}.linter-setting-header{margin-bottom:24px;overflow-x:auto;overflow-y:hidden}.linter-setting-header .linter-setting-tab-group{align-items:flex-end;display:flex;flex-wrap:wrap;width:100%}.linter-setting-tab-group{border-bottom:2px solid var(--background-modifier-border);margin-top:6px;padding-left:2px;padding-right:2px}.linter-setting-header .linter-tab-settings{border-left:2px solid transparent;border-right:2px solid transparent;cursor:pointer;font-weight:600;padding:6px 12px;white-space:nowrap}.linter-setting-header .linter-tab-settings:first-child{margin-left:6px}.linter-setting-header .linter-tab-settings.linter-tab-settings-active{border:2px solid var(--background-modifier-border);border-bottom-color:var(--background-primary);border-radius:2px;transform:translateY(2px)}.linter-navigation-item:not(.linter-navigation-item-selected)>span:nth-child(2),.linter-visually-hidden{border:0;clip:rect(0 0 0 0);clip-path:rect(0 0 0 0);height:auto;margin:0;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}textarea.full-width{margin-bottom:.8em;margin-top:.8em;min-height:10em;width:100%}.full-width-textbox-input-wrapper{position:relative}.settings-copy-button{margin:0 0 0 auto;padding:4px;position:absolute;right:.8em;top:.8em}.settings-copy-button svg.linter-clipboard path{fill:var(--text-faint)}.settings-copy-button svg.linter-success path{fill:var(--interactive-success)}.settings-copy-button:active,.settings-copy-button:hover{cursor:pointer}.settings-copy-button:active svg path,.settings-copy-button:hover svg path{fill:var(--text-accent-hover);transition:all .3s ease}.settings-copy-button:focus{outline:0}.linter-custom-regex-replacement-container div:last-child{border:none}.linter-custom-regex-replacement{border:none;border-bottom:var(--hr-thickness) solid;border-color:var(--hr-color);margin-bottom:15px}.linter-custom-regex-replacement-row2{flex-wrap:wrap}.linter-custom-regex-replacement-normal-input{width:40%}.linter-custom-regex-replacement-flags{width:15%}.linter-custom-regex-replacement-label{flex-direction:row-reverse}.linter-custom-regex-replacement-label-input{width:50%}.linter-files-to-ignore-container div:last-child{border:none}.linter-files-to-ignore{border:none;border-bottom:var(--hr-thickness) solid;border-color:var(--hr-color);margin-bottom:15px}.linter-files-to-ignore-normal-input{width:40%}.linter-files-to-ignore-flags{width:15%}.linter-no-border{border:none}.linter-border-bottom{border-bottom:1px solid var(--background-modifier-border);border-top:0;margin-bottom:.75em}.linter-no-padding-top{padding-top:0}.custom-row-description{margin-top:0}.modal-warn,.search-zero-state{font-weight:700}.modal-heading,.search-zero-state{text-align:center} 
.pandoc-plugin-error {
    color: red;
}
 .zt-format {
  border: 1px solid var(--background-modifier-border);
  padding: 1rem;
  background-color: var(--background-primary);
  border-radius: 10px;
  margin-bottom: 10px;
}

.zt-format__form {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-bottom: 1rem;
  max-width: 600px;
}

.zt-format__form:last-child {
  margin-bottom: 0;
}

.zt-format__label {
  font-size: 0.9em;
  font-weight: 600;
  margin-bottom: 5px;
}

.is-deprecated .zt-format__label {
  color: var(--text-error);
}

.zt-format__input-wrapper {
  display: flex;
  align-items: center;
}

.zt-format__input-wrapper textarea {
  resize: vertical;
}

.zt-format__input-wrapper > *:not(.checkbox-container) {
  width: 100% !important;
}

.is-deprecated .zt-format__input-wrapper button {
  width: auto !important;
  flex-grow: 0;
  flex-shrink: 0;
  margin-left: 5px;
}

.zt-format__delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 7px 9px;
  margin-left: 10px;
  flex-shrink: 0;
  flex-grow: 0;
}

.zt-json-viewer {
  font-size: 13px;
}

.zt-json-viewer .react-json-view {
  padding: 1em;
  border-radius: 10px;
  margin-top: 1em;
  overflow: auto;
  font-family: var(--font-monospace) !important;
}

.zt-json-viewer__btns {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.zt-json-viewer__btns label {
  display: block;
  font-weight: bold;
  padding-top: 1em;
}

.zt-json-viewer__btns select {
  font-size: 1em;
}

.zt-json-viewer__btns button {
  font-size: 1em;
  margin-right: 5px;
}

.zt-json-viewer__preview,
.zt-json-viewer__data {
  border: 1px solid var(--background-modifier-border);
  border-radius: 10px;
  padding: 1em;
  margin-top: 1em;
}

.zt-json-viewer__preview.error {
  background-color: #ff000011;
  font-family: var(--font-monospace);
}

.zt-json-viewer__preview pre {
  overflow: auto;
  white-space: pre-wrap;
  margin: 0;
}

.zt-json-viewer__preview pre,
.zt-json-viewer__preview code {
  font-family: inherit;
}

.zt-json-viewer__preview:not(.error) pre {
  font-family: var(--font-text, --font-default, --default-font);
  max-height: 70vh;
  min-height: 400px;
}

.zt-multiselect {
  width: 300px;
  text-align: left;
}

.zt-multiselect input {
  outline: none !important;
  box-shadow: none !important;
}

.zt-format__input-note {
  font-style: italic;
  font-size: 0.9em;
  padding-top: 10px;
  margin-bottom: 10px;
}

.zt-setting-item pre,
.zt-format__input-note pre {
  display: inline-block;
  margin: 0;
  padding: 0 6px;
  background-color: var(--background-secondary-alt);
  border-radius: 4px;
}

.zt-asset-success {
  text-align: left;
  display: flex;
}

.zt-asset-success__icon {
  color: var(--interactive-success);
  font-size: 24px;
  margin-right: 5px;
}

.zt-asset-success__icon svg {
  width: 1em !important;
  height: 1em !important;
}

.zt-asset-success__message {
  font-size: 0.9em;
}

.zt-suggest-title {
  font-size: var(--font-ui-small);
  color: var(--text-muted);
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-top: var(--size-4-1);
}

.zt-suggest-loading-wrapper {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  padding: var(--size-4-2) 0;
}

.zt-suggest-loading,
.zt-suggest-loading:before,
.zt-suggest-loading:after {
  border-radius: 999px;
  width: 1em;
  height: 1em;
  animation-fill-mode: both;
  animation: bblFadInOut 1.6s infinite ease-in-out;
}

.zt-suggest-loading {
  display: block;
  color: var(--text-muted);
  font-size: 7px;
  position: relative;
  animation-delay: -0.16s;
  top: -1em;
}
.zt-suggest-loading:before,
.zt-suggest-loading:after {
  content: '';
  position: absolute;
}
.zt-suggest-loading:before {
  left: -2em;
  animation-delay: -0.32s;
}
.zt-suggest-loading:after {
  left: 2em;
}

.zt-color-chip {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  margin-right: var(--size-4-1);
}

@keyframes bblFadInOut {
  0%,
  80%,
  100% {
    box-shadow: 0 1em 0 -1.3em;
  }
  40% {
    box-shadow: 0 1em 0 0;
  }
}
 *,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(147,197,253,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(147,197,253,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }.tw-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.tw-pointer-events-none{pointer-events:none}.tw-fixed{position:fixed}.tw-absolute{position:absolute}.tw-relative{position:relative}.tw-inset-0{inset:0}.tw-inset-x-0{left:0;right:0}.tw-inset-y-0{top:0;bottom:0}.tw-bottom-0{bottom:0}.tw-bottom-2{bottom:var(--size-4-2)}.tw-left-1\/2{left:50%}.tw-left-2{left:var(--size-4-2)}.tw-right-0{right:0}.tw-right-2{right:var(--size-4-2)}.tw-right-3{right:var(--size-4-3)}.tw-right-4{right:var(--size-4-4)}.tw-right-8{right:2rem}.tw-top-1\/2{top:50%}.tw-top-2{top:var(--size-4-2)}.tw-top-4{top:var(--size-4-4)}.tw-z-\[100\]{z-index:100}.tw-z-\[1\]{z-index:1}.tw-z-\[50\]{z-index:50}.tw-z-\[9999\]{z-index:9999}.tw-z-modal{z-index:var(--layer-modal)}.tw-z-popover{z-index:var(--layer-popover)}.tw-col-span-3{grid-column:span 3/span 3}.tw-m-0{margin:0}.-tw-mx-1{margin-left:calc(var(--size-4-1)*-1);margin-right:calc(var(--size-4-1)*-1)}.tw-mx-2{margin-left:var(--size-4-2);margin-right:var(--size-4-2)}.tw-mx-auto{margin-left:auto;margin-right:auto}.tw-my-1{margin-top:var(--size-4-1);margin-bottom:var(--size-4-1)}.tw-my-2{margin-top:var(--size-4-2);margin-bottom:var(--size-4-2)}.tw--mt-1{margin-top:calc(var(--size-4-1)*-1)}.tw-mb-1{margin-bottom:var(--size-4-1)}.tw-mb-2{margin-bottom:var(--size-4-2)}.tw-mb-3{margin-bottom:var(--size-4-3)}.tw-mb-4{margin-bottom:var(--size-4-4)}.tw-ml-1{margin-left:var(--size-4-1)}.tw-ml-auto{margin-left:auto}.tw-mr-1{margin-right:var(--size-4-1)}.tw-mr-2{margin-right:var(--size-4-2)}.tw-mt-0{margin-top:0}.tw-mt-0\.5{margin-top:.125rem}.tw-mt-1{margin-top:var(--size-4-1)}.tw-mt-2{margin-top:var(--size-4-2)}.tw-mt-4{margin-top:var(--size-4-4)}.tw-mt-auto{margin-top:auto}.tw-box-border{box-sizing:border-box}.tw-line-clamp-2{-webkit-line-clamp:2}.tw-line-clamp-2,.tw-line-clamp-3{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical}.tw-line-clamp-3{-webkit-line-clamp:3}.tw-block{display:block}.tw-inline-block{display:inline-block}.tw-flex{display:flex}.tw-inline-flex{display:inline-flex}.tw-grid{display:grid}.tw-hidden{display:none}.\!tw-size-3{width:var(--size-4-3)!important;height:var(--size-4-3)!important}.tw-size-12{width:3rem;height:3rem}.tw-size-2{width:var(--size-4-2);height:var(--size-4-2)}.tw-size-3{width:var(--size-4-3);height:var(--size-4-3)}.tw-size-3\.5{width:.875rem;height:.875rem}.tw-size-4{width:var(--size-4-4);height:var(--size-4-4)}.tw-size-5{width:var(--size-4-5);height:var(--size-4-5)}.tw-size-6{width:var(--size-4-6);height:var(--size-4-6)}.tw-size-7{width:1.75rem;height:1.75rem}.tw-size-full{width:100%;height:100%}.\!tw-h-9{height:2.25rem!important}.tw-h-1\.5{height:.375rem}.tw-h-10{height:2.5rem}.tw-h-20{height:5rem}.tw-h-4{height:var(--size-4-4)}.tw-h-5\.5{height:calc(var(--size-4-5) + 2px)}.tw-h-6{height:var(--size-4-6)}.tw-h-60{height:15rem}.tw-h-8{height:2rem}.tw-h-9{height:2.25rem}.tw-h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.tw-h-full{height:100%}.tw-h-px{height:1px}.tw-max-h-0{max-height:0}.tw-max-h-12{max-height:3rem}.tw-max-h-20{max-height:5rem}.tw-max-h-40{max-height:10rem}.tw-max-h-6{max-height:var(--size-4-6)}.tw-max-h-60{max-height:15rem}.tw-max-h-96{max-height:24rem}.tw-max-h-\[200px\]{max-height:200px}.tw-max-h-\[300px\]{max-height:300px}.tw-max-h-\[400px\]{max-height:400px}.tw-max-h-\[600px\]{max-height:600px}.tw-max-h-\[calc\(3\*5\.7rem\)\]{max-height:17.1rem}.tw-max-h-screen{max-height:100vh}.\!tw-min-h-32{min-height:8rem!important}.tw-min-h-14{min-height:3.5rem}.tw-min-h-20{min-height:5rem}.tw-min-h-32{min-height:8rem}.tw-min-h-\[200px\]{min-height:200px}.tw-min-h-\[60px\]{min-height:60px}.tw-min-h-\[80px\]{min-height:80px}.tw-w-10{width:2.5rem}.tw-w-16{width:4rem}.tw-w-20{width:5rem}.tw-w-4{width:var(--size-4-4)}.tw-w-6{width:var(--size-4-6)}.tw-w-64{width:16rem}.tw-w-72{width:18rem}.tw-w-9{width:2.25rem}.tw-w-\[100px\]{width:100px}.tw-w-\[120px\]{width:120px}.tw-w-\[180px\]{width:180px}.tw-w-\[200px\]{width:200px}.tw-w-\[320px\]{width:320px}.tw-w-\[72px\]{width:72px}.tw-w-\[80px\]{width:80px}.tw-w-fit{width:-moz-fit-content;width:fit-content}.tw-w-full{width:100%}.\!tw-min-w-\[50px\]{min-width:50px!important}.tw-min-w-20{min-width:5rem}.tw-min-w-32{min-width:8rem}.tw-min-w-72{min-width:18rem}.tw-min-w-\[40px\]{min-width:40px}.tw-min-w-\[80px\]{min-width:80px}.tw-min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.tw-min-w-fit{min-width:-moz-fit-content;min-width:fit-content}.\!tw-max-w-full{max-width:100%!important}.tw-max-w-0{max-width:0}.tw-max-w-40{max-width:10rem}.tw-max-w-64{max-width:16rem}.tw-max-w-80{max-width:20rem}.tw-max-w-96{max-width:24rem}.tw-max-w-\[100px\]{max-width:100px}.tw-max-w-\[16px\]{max-width:16px}.tw-max-w-\[600px\]{max-width:600px}.tw-max-w-full{max-width:100%}.tw-max-w-lg{max-width:32rem}.tw-flex-1{flex:1 1 0%}.tw-shrink-0{flex-shrink:0}.tw-grow{flex-grow:1}.tw-caption-bottom{caption-side:bottom}.tw-border-collapse{border-collapse:collapse}.-tw-translate-x-1\/2{--tw-translate-x:-50%}.-tw-translate-x-1\/2,.-tw-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-tw-translate-y-1\/2{--tw-translate-y:-50%}.tw--translate-x-4{--tw-translate-x:calc(var(--size-4-4)*-1)}.tw--translate-x-4,.tw-translate-x-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-translate-x-0{--tw-translate-x:0px}.tw-translate-x-0\.5{--tw-translate-x:0.125rem}.tw-translate-x-0\.5,.tw-translate-x-5\.5{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-translate-x-5\.5{--tw-translate-x:calc(var(--size-4-5) + 2px)}.tw-translate-y-0{--tw-translate-y:0px}.tw-translate-y-0,.tw-translate-y-2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-translate-y-2{--tw-translate-y:var(--size-4-2)}.tw-transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-transform-gpu{transform:translate3d(var(--tw-translate-x),var(--tw-translate-y),0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes tw-spin{to{transform:rotate(1turn)}}.tw-animate-spin{animation:tw-spin 1s linear infinite}.tw-cursor-default{cursor:default}.tw-cursor-grab{cursor:grab}.tw-cursor-grabbing{cursor:grabbing}.tw-cursor-not-allowed{cursor:not-allowed}.tw-cursor-pointer{cursor:var(--cursor-link)}.tw-touch-none{touch-action:none}.tw-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.tw-select-text{-webkit-user-select:text;-moz-user-select:text;user-select:text}.tw-resize-none{resize:none}.tw-resize-y{resize:vertical}.tw-list-inside{list-style-position:inside}.tw-list-disc{list-style-type:disc}.tw-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.tw-grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.tw-grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.tw-grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.tw-flex-row{flex-direction:row}.tw-flex-col{flex-direction:column}.tw-flex-col-reverse{flex-direction:column-reverse}.tw-flex-wrap{flex-wrap:wrap}.tw-items-start{align-items:flex-start}.tw-items-end{align-items:flex-end}.tw-items-center{align-items:center}.tw-justify-start{justify-content:flex-start}.tw-justify-end{justify-content:flex-end}.tw-justify-center{justify-content:center}.tw-justify-between{justify-content:space-between}.tw-justify-around{justify-content:space-around}.tw-gap-0\.5{gap:.125rem}.tw-gap-1{gap:var(--size-4-1)}.tw-gap-1\.5{gap:.375rem}.tw-gap-2{gap:var(--size-4-2)}.tw-gap-3{gap:var(--size-4-3)}.tw-gap-4{gap:var(--size-4-4)}.tw-gap-x-2{-moz-column-gap:var(--size-4-2);column-gap:var(--size-4-2)}.tw-gap-y-1{row-gap:var(--size-4-1)}.tw-space-x-1>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(var(--size-4-1)*var(--tw-space-x-reverse));margin-left:calc(var(--size-4-1)*(1 - var(--tw-space-x-reverse)))}.tw-space-y-0\.5>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(.125rem*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.125rem*var(--tw-space-y-reverse))}.tw-space-y-1>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(var(--size-4-1)*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(var(--size-4-1)*var(--tw-space-y-reverse))}.tw-space-y-1\.5>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(.375rem*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.375rem*var(--tw-space-y-reverse))}.tw-space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(var(--size-4-2)*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(var(--size-4-2)*var(--tw-space-y-reverse))}.tw-space-y-3>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(var(--size-4-3)*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(var(--size-4-3)*var(--tw-space-y-reverse))}.tw-space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(var(--size-4-4)*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(var(--size-4-4)*var(--tw-space-y-reverse))}.tw-space-y-6>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(var(--size-4-6)*(1 - var(--tw-space-y-reverse)));margin-bottom:calc(var(--size-4-6)*var(--tw-space-y-reverse))}.tw-self-end{align-self:flex-end}.tw-overflow-auto{overflow:auto}.tw-overflow-hidden{overflow:hidden}.tw-overflow-y-auto{overflow-y:auto}.tw-overflow-y-hidden{overflow-y:hidden}.tw-scroll-smooth{scroll-behavior:smooth}.tw-truncate{overflow:hidden;text-overflow:ellipsis}.tw-truncate,.tw-whitespace-nowrap{white-space:nowrap}.tw-whitespace-pre-wrap{white-space:pre-wrap}.tw-text-wrap{text-wrap:wrap}.tw-break-words{overflow-wrap:break-word}.tw-break-all{word-break:break-all}.\!tw-rounded-md{border-radius:var(--radius-m)!important}.tw-rounded-full{border-radius:9999px}.tw-rounded-lg{border-radius:var(--radius-l)}.tw-rounded-md{border-radius:var(--radius-m)}.tw-rounded-sm{border-radius:var(--radius-s)}.tw-rounded-xl{border-radius:var(--radius-xl)}.tw-rounded-b-\[2px\]{border-bottom-right-radius:2px;border-bottom-left-radius:2px}.tw-rounded-t-md{border-top-left-radius:var(--radius-m);border-top-right-radius:var(--radius-m)}.\!tw-border{border-width:var(--border-width)!important}.tw-border{border-width:var(--border-width)}.tw-border-\[0px\]{border-width:0}.tw-border-b{border-bottom-width:var(--border-width)}.tw-border-t{border-top-width:var(--border-width)}.tw-border-solid{border-style:solid}.tw-border-dashed{border-style:dashed}.tw-border-none{border-style:none}.tw-border-border{border-color:var(--background-modifier-border)}.tw-border-interactive-accent{border-color:var(--interactive-accent)}.tw-border-transparent{border-color:transparent}.tw-border-b-border{border-bottom-color:var(--background-modifier-border)}.\!tw-bg-dropdown{background-color:var(--dropdown-background)!important}.\!tw-bg-interactive-accent{background-color:var(--interactive-accent)!important}.\!tw-bg-transparent{background-color:transparent!important}.tw-bg-\[--background-modifier-border-hover\]{background-color:var(--background-modifier-border-hover)}.tw-bg-dropdown{background-color:var(--dropdown-background)}.tw-bg-error{background-color:rgba(var(--color-red-rgb),.2)}.tw-bg-interactive-accent{background-color:var(--interactive-accent)}.tw-bg-modifier-error{background-color:var(--background-modifier-error)}.tw-bg-modifier-success{background-color:var(--background-modifier-success)}.tw-bg-overlay\/50{background-color:rgba(0,0,0,.5)}.tw-bg-primary{background-color:var(--background-primary)}.tw-bg-primary-alt{background-color:var(--background-primary-alt)}.tw-bg-secondary{background-color:var(--background-secondary)}.tw-bg-secondary-alt{background-color:var(--background-secondary-alt)}.tw-bg-success{background-color:rgba(var(--color-green-rgb),.2)}.tw-bg-toggle-thumb{background-color:var(--toggle-thumb-color)}.tw-bg-transparent{background-color:transparent}.tw-bg-\[linear-gradient\(to_top\,var\(--background-primary\)_0\%\,var\(--background-primary\)_30\%\,transparent_100\%\)\]{background-image:linear-gradient(to top,var(--background-primary) 0,var(--background-primary) 30%,transparent 100%)}.tw-fill-current{fill:currentColor}.tw-stroke-\[7\]{stroke-width:7}.tw-p-0{padding:0}.tw-p-0\.5{padding:.125rem}.tw-p-1{padding:var(--size-4-1)}.tw-p-2{padding:var(--size-4-2)}.tw-p-3{padding:var(--size-4-3)}.tw-p-4{padding:var(--size-4-4)}.tw-p-6{padding:var(--size-4-6)}.tw-p-8{padding:2rem}.\!tw-px-3{padding-left:var(--size-4-3)!important;padding-right:var(--size-4-3)!important}.\!tw-py-1{padding-top:var(--size-4-1)!important;padding-bottom:var(--size-4-1)!important}.tw-px-1{padding-left:var(--size-4-1);padding-right:var(--size-4-1)}.tw-px-2{padding-left:var(--size-4-2);padding-right:var(--size-4-2)}.tw-px-2\.5{padding-left:.625rem;padding-right:.625rem}.tw-px-3{padding-left:var(--size-4-3);padding-right:var(--size-4-3)}.tw-px-4{padding-left:var(--size-4-4);padding-right:var(--size-4-4)}.tw-px-8{padding-left:2rem;padding-right:2rem}.tw-py-0{padding-top:0;padding-bottom:0}.tw-py-0\.5{padding-top:.125rem;padding-bottom:.125rem}.tw-py-1{padding-top:var(--size-4-1);padding-bottom:var(--size-4-1)}.tw-py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.tw-py-2{padding-top:var(--size-4-2);padding-bottom:var(--size-4-2)}.tw-py-3{padding-top:var(--size-4-3);padding-bottom:var(--size-4-3)}.tw-py-4{padding-top:var(--size-4-4);padding-bottom:var(--size-4-4)}.tw-py-8{padding-top:2rem;padding-bottom:2rem}.\!tw-pr-7{padding-right:1.75rem!important}.tw-pb-0\.5{padding-bottom:.125rem}.tw-pb-1{padding-bottom:var(--size-4-1)}.tw-pb-2{padding-bottom:var(--size-4-2)}.tw-pb-4{padding-bottom:var(--size-4-4)}.tw-pb-6{padding-bottom:var(--size-4-6)}.tw-pl-0{padding-left:0}.tw-pl-0\.5{padding-left:.125rem}.tw-pl-1{padding-left:var(--size-4-1)}.tw-pl-2{padding-left:var(--size-4-2)}.tw-pl-3{padding-left:var(--size-4-3)}.tw-pl-4{padding-left:var(--size-4-4)}.tw-pl-8{padding-left:2rem}.tw-pr-0\.5{padding-right:.125rem}.tw-pr-1{padding-right:var(--size-4-1)}.tw-pr-2{padding-right:var(--size-4-2)}.tw-pr-8{padding-right:2rem}.tw-pt-0{padding-top:0}.tw-pt-2{padding-top:var(--size-4-2)}.tw-pt-3{padding-top:var(--size-4-3)}.tw-pt-4{padding-top:var(--size-4-4)}.tw-text-left{text-align:left}.tw-text-center{text-align:center}.tw-text-right{text-align:right}.tw-align-middle{vertical-align:middle}.tw-font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.\!tw-text-sm{font-size:.875rem!important;line-height:1.25rem!important}.tw-text-\[10px\]{font-size:10px}.tw-text-\[12px\]{font-size:12px}.tw-text-\[13px\]{font-size:13px}.tw-text-\[calc\(var\(--font-text-size\)_-_2px\)\]{font-size:calc(var(--font-text-size) - 2px)}.tw-text-base{font-size:1rem;line-height:1.5rem}.tw-text-lg{font-size:1.125rem;line-height:1.75rem}.tw-text-sm{font-size:.875rem;line-height:1.25rem}.tw-text-text{font-size:var(--font-text-size)}.tw-text-ui-smaller{font-size:var(--font-ui-smaller)}.tw-text-xl{font-size:1.25rem;line-height:1.75rem}.tw-text-xs{font-size:.75rem;line-height:1rem}.tw-font-bold{font-weight:var(--font-bold)}.tw-font-medium{font-weight:var(--font-medium)}.tw-font-normal{font-weight:var(--font-normal)}.tw-font-semibold{font-weight:var(--font-semibold)}.tw-leading-4{line-height:1rem}.tw-leading-none{line-height:1}.tw-tracking-tight{letter-spacing:-.025em}.tw-tracking-widest{letter-spacing:.1em}.tw-text-accent{color:var(--text-accent)}.tw-text-callout-warning{--tw-text-opacity:1;color:rgba(var(--callout-warning),var(--tw-text-opacity,1))}.tw-text-current{color:currentColor}.tw-text-error{color:var(--text-error)}.tw-text-faint{color:var(--text-faint)}.tw-text-model-capabilities-blue{color:var(--color-blue)}.tw-text-model-capabilities-green{color:var(--color-green)}.tw-text-muted{color:var(--text-muted)}.tw-text-normal{color:var(--text-normal)}.tw-text-on-accent{color:var(--text-on-accent)}.tw-text-success{color:var(--text-success)}.tw-text-warning{color:var(--text-warning)}.tw-underline-offset-4{text-underline-offset:4px}.tw-opacity-0{opacity:0}.tw-opacity-100{opacity:1}.tw-opacity-50{opacity:.5}.tw-opacity-60{opacity:.6}.tw-opacity-75{opacity:.75}.tw-opacity-80{opacity:.8}.tw-opacity-90{opacity:.9}.\!tw-shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1)!important;--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color)!important}.\!tw-shadow,.\!tw-shadow-none{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.\!tw-shadow-none{--tw-shadow:0 0 #0000!important;--tw-shadow-colored:0 0 #0000!important}.\!tw-shadow-sm{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05)!important;--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color)!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.tw-shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color)}.tw-shadow,.tw-shadow-lg{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.tw-shadow-lg{--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color)}.tw-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color)}.tw-shadow-md,.tw-shadow-sm{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.tw-shadow-sm{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color)}.tw-outline-none{outline:2px solid transparent;outline-offset:2px}.tw-ring-0{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.tw-ring-offset-ring{--tw-ring-offset-color:var(--interactive-accent)}.tw-backdrop-blur-sm{--tw-backdrop-blur:blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.\!tw-transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke!important;transition-timing-function:cubic-bezier(.4,0,.2,1)!important;transition-duration:.15s!important}.tw-transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.tw-transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.tw-transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.tw-transition-shadow{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.tw-transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.tw-delay-200{transition-delay:.2s}.tw-duration-200{transition-duration:.2s}.tw-duration-300{transition-duration:.3s}.tw-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0) scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1)) rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0) scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1)) rotate(var(--tw-exit-rotate,0))}}.tw-animate-in{animation-name:enter;animation-duration:.15s;--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial}.tw-animate-out{animation-name:exit;animation-duration:.15s;--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial}.tw-fade-in-0{--tw-enter-opacity:0}.tw-zoom-in-95{--tw-enter-scale:.95}.tw-slide-in-from-top{--tw-enter-translate-y:-100%}.tw-slide-out-to-top{--tw-exit-translate-y:-100%}.tw-duration-200{animation-duration:.2s}.tw-duration-300{animation-duration:.3s}.tw-delay-200{animation-delay:.2s}.tw-ease-in-out{animation-timing-function:cubic-bezier(.4,0,.2,1)}.tw-text-muted\/50{color:color-mix(in srgb,var(--text-muted) 50%,transparent)}.tw-text-muted\/60{color:color-mix(in srgb,var(--text-muted) 60%,transparent)}.tw-text-muted\/80{color:color-mix(in srgb,var(--text-muted) 80%,transparent)}.tw-border-accent\/50{border-color:color-mix(in srgb,var(--text-accent) 50%,transparent)}.tw-text-accent\/70{color:color-mix(in srgb,var(--text-accent) 70%,transparent)}.tw-bg-primary\/90{background-color:color-mix(in srgb,var(--background-primary) 90%,transparent)}.tw-bg-primary-alt\/50{background-color:color-mix(in srgb,var(--background-primary-alt) 50%,transparent)}.tw-bg-secondary\/30{background-color:color-mix(in srgb,var(--background-secondary) 30%,transparent)}.tw-bg-secondary\/40{background-color:color-mix(in srgb,var(--background-secondary) 40%,transparent)}.tw-bg-interactive-accent\/20{background-color:color-mix(in srgb,var(--interactive-accent) 20%,transparent)}.tw-border-interactive-accent\/30{border-color:color-mix(in srgb,var(--interactive-accent) 30%,transparent)}.tw-\@container\/chat-input{container-type:inline-size;container-name:chat-input}[data-radix-popper-content-wrapper]{z-index:9999!important}.button-container{display:flex;justify-content:space-between;align-items:center;gap:1rem;padding:0 25px}.button-container .mod-cta{flex:1;margin:25px 0}@media screen and (max-width:768px){.button-container{gap:.5rem;padding:0 12px}.button-container .mod-cta{margin:12px 0;font-size:.9rem;padding:6px 12px}}.warning-message{background-color:#000;color:orange;padding:10px;margin:10px 0;border-radius:5px;border:1px solid orange;font-weight:bolder;text-align:center}.copilot-setting-item-name{font-weight:700;display:block;color:var(--inline-title-color);margin-top:20px;margin-bottom:10px}.copilot-setting-item-description{display:block;margin-top:10px;margin-bottom:10px}.copilot-setting-item-control{width:50%;max-width:100%}.copilot-setting-item-control::-moz-placeholder{color:gray;opacity:.5}.copilot-setting-item-control::placeholder{color:gray;opacity:.5}.copilot-setting-item-control[type=range]{width:70%}.chat-context-menu{display:flex;align-items:center;gap:8px;padding:0;border-bottom:none;flex:1}.note-badge{font-size:10px;padding:0 4px;background:rgba(0,0,0,.2);border-radius:3px}.remove-note{padding:0 4px;cursor:pointer;opacity:.5;background:none!important;border:none!important;box-shadow:none!important}.remove-note:hover{opacity:1}.context-note .note-name{max-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block}.context-note.url .note-badge{background-color:var(--interactive-accent)}.context-note.url .note-name{max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.context-note .note-badge.pdf{background-color:var(--background-modifier-error)}.chat-icons-right{display:flex;align-items:center;gap:4px;justify-content:flex-end;margin-left:auto}.icon-scaler{transform:scale(.7)}.tooltip-text{max-width:400px;background-color:var(--background-secondary);color:var(--text-normal);text-align:center;border-radius:5px;padding:5px;font-size:var(--font-ui-smaller);white-space:nowrap}.select-wrapper{position:relative;display:inline-block}.chain-select-button,.chat-icon-button.clickable-icon,.chat-icon-selection,.model-select-button,.submit-button{display:flex;align-items:center;gap:2px;padding:0;background:none!important;border:none!important;box-shadow:none!important;color:var(--text-muted);font-size:10px;cursor:pointer;opacity:.5;transition:opacity .2s ease}.chain-select-button:hover,.chat-icon-button.clickable-icon:hover,.chat-icon-selection:hover,.model-select-button:hover,.submit-button:hover{background:none!important;color:var(--text-normal);opacity:1}@keyframes flash{0%,to{opacity:.5}50%{opacity:1}}.submit-button.cancel{opacity:.5;padding:4px;border-radius:4px;animation:flash 2s infinite}.submit-button.cancel:hover{opacity:1}.selected-images{display:flex;flex-wrap:wrap;gap:8px;padding:8px;background:var(--background-secondary);border-radius:4px;margin-bottom:8px}.image-preview-container{position:relative;width:80px;height:80px}.selected-image-preview{width:100%;height:100%;-o-object-fit:cover;object-fit:cover;border-radius:4px;border:1px solid var(--background-modifier-border)}.remove-image-button{position:absolute;top:-8px;right:-8px;width:20px;height:20px;border-radius:50%;background:var(--background-modifier-error);color:#fff;border:none;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:14px;padding:0;line-height:1}.remove-image-button:hover{background:var(--background-modifier-error-hover)}.button-content{display:flex;align-items:center}.button-content span{font-size:10px}.button-content .icon-scaler{width:18px;height:18px}.model-select-content{min-width:120px;background:var(--background-secondary);border:1px solid var(--background-modifier-border);border-radius:4px;padding:4px;font-size:10px;z-index:1000}.model-select-content [role=menuitem]{padding:6px 12px;cursor:pointer;border-radius:2px;font-size:10px;color:var(--text-normal);margin:2px 0}.model-select-content [role=menuitem]:hover{background:var(--background-modifier-hover)}.chain-select-button svg,.model-select-button svg,.submit-button svg{width:10px;height:10px}.chain-select-button:hover{opacity:1;background:var(--background-modifier-hover)}.chain-select-content{min-width:120px;background:var(--background-secondary);border:1px solid var(--background-modifier-border);border-radius:4px;padding:4px;font-size:10px;z-index:1000}.chain-select-content [role=menuitem]{padding:6px 12px;cursor:pointer;border-radius:2px;font-size:10px;color:var(--text-normal);margin:2px 0}.chain-select-content [role=menuitem]:hover{background:var(--background-modifier-hover)}.chain-select-button svg{width:12px;height:12px}.chain-select-button span{opacity:1}.chat-icon-button{position:relative}.chain-select-button svg,.model-select-button svg,.submit-button svg{width:12px;height:12px}.message{display:flex;padding:0;border-radius:4px;position:relative;margin-bottom:0}.message-content{word-wrap:break-word;overflow-wrap:break-word;word-break:break-word;line-height:1.6!important}.message-content p{margin-top:0;margin-bottom:0}.message-content table{margin-top:15px;margin-bottom:15px}.message-content code{background-color:var(--code-background);padding:2px 4px;border-radius:3px;color:var(--code-normal)}.message-content pre{background-color:var(--background-primary-alt);border-radius:4px;padding:10px;border:1px solid var(--background-modifier-border)}.message-content pre code{background-color:transparent;padding:0;color:var(--code-normal);line-height:1.5!important;display:block;white-space:pre-wrap;word-wrap:break-word;overflow-wrap:break-word}.message-content pre{position:relative}.message-content pre .copy-code-button{position:absolute;top:0;right:0;padding:4px 8px;color:var(--text-muted);background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:0 4px 0 4px;font-size:.8em;transition:all .1s ease;opacity:0}.message-content pre:hover .copy-code-button{opacity:1}.message-content pre .copy-code-button:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.message-content ol,.message-content ul{padding-left:20px;margin:10px}.message-image-content{width:100%;max-width:400px;padding:8px 0}.chat-message-image{width:100%;height:auto;-o-object-fit:contain;object-fit:contain;border-radius:8px;border:1px solid var(--background-modifier-border)}.copilot-command-modal{display:flex;flex-direction:column;align-items:center;gap:10px}.copilot-command-input-container{width:90%;margin:auto}.copilot-command-input-container input,.copilot-command-input-container textarea{display:block;width:100%;margin-top:10px}.copilot-command-input-container textarea{height:150px;resize:vertical}.copilot-command-save-btn-container{display:flex;justify-content:center;align-items:center;text-align:center}.copilot-command-save-btn{margin-top:15px}.model-settings-table{border-collapse:collapse;width:100%}.model-settings-table td,.model-settings-table th{text-align:center;vertical-align:middle;padding:.5em;border:none!important;height:2.5em}.model-settings-table th{font-weight:700}.model-settings-table th:first-child{width:10%}.model-settings-table th:nth-child(2){width:50%}.model-settings-table th:nth-child(3){width:16%}.model-settings-table th:nth-child(4),.model-settings-table th:nth-child(5),.model-settings-table th:nth-child(6){width:8%}.model-settings-table .switch,.model-settings-table td .copilot-setting-item{display:flex;justify-content:center;align-items:center;height:100%;margin:0}.add-custom-model{margin-top:20px}.switch.disabled{opacity:.5;cursor:not-allowed}.switch.disabled input:checked+.slider{background-color:#ccc}.switch.disabled input:focus+.slider{box-shadow:0 0 1px #ccc}.switch.disabled input:checked+.slider:before{transform:translateX(26px)}.model-select{max-width:150px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.select-wrapper:after{content:"\25BC";position:absolute;top:50%;right:10px;transform:translateY(-50%);pointer-events:none}.add-model-button{margin-top:15px}.edit-textarea{width:100%;min-height:40px;padding:8px;font-size:14px;line-height:1.5;border:1px solid #ccc;border-radius:4px;resize:vertical;overflow-y:hidden}.edit-textarea:focus{outline:none;box-shadow:0 0 0 2px rgba(0,123,255,.25)}.copilot-notice-container{display:flex;flex-direction:column;align-items:stretch;width:100%;padding:10px;box-sizing:border-box}.copilot-notice-message{margin-bottom:16px}.copilot-notice-container button{align-self:flex-end;margin-top:16px;margin-left:auto}.similar-notes-modal-container{width:80vw;height:80vh}.similar-notes-modal{max-width:100%;max-height:100%}.similar-notes-container{max-height:calc(80vh - 100px);overflow-y:auto;padding:10px}.similar-note-item{margin-bottom:10px}.similar-note-title{cursor:pointer;color:var(--text-accent)}.similar-note-item details>summary{list-style:none}.similar-note-item details>summary:before{content:"▶";display:inline-block;width:20px;transition:transform .3s}.similar-note-item details[open]>summary:before{transform:rotate(90deg)}.similar-note-item details>p{margin-left:20px;margin-top:5px}.desktop-only{display:table}.mobile-only{display:none}@media screen and (max-width:768px){.desktop-only{display:none}.mobile-only{display:block}.model-cards-container{display:flex;flex-direction:column;gap:16px;padding:8px}.model-card{background:var(--background-primary-alt);border-radius:8px;padding:0 16px;box-shadow:0 2px 4px rgba(0,0,0,.1)}.model-card-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;padding-bottom:8px;border-bottom:1px solid var(--background-modifier-border)}.model-card-header h3{margin:0;font-size:1.1em;color:var(--text-normal)}.model-provider{font-size:.9em;color:var(--text-muted);padding:2px 0;background:var(--background-secondary);border-radius:4px;width:-moz-fit-content;width:fit-content;align-self:flex-start}.model-card-content{display:flex;flex-direction:column;gap:12px}.model-card-controls{justify-content:space-around;padding:8px 0}.model-card-controls,.model-card-item{display:flex;align-items:center;gap:8px}.model-card-item{flex-direction:row}.model-card-item span{color:var(--text-muted);font-size:.9em;white-space:nowrap}.model-card-item .switch{margin:0}.chain-select-content,.model-select-content{background:var(--background-primary);box-shadow:0 2px 8px rgba(0,0,0,.2)}.chain-select-content [role=menuitem],.model-select-content [role=menuitem]{background:var(--background-primary);color:var(--text-normal);padding:8px 12px;font-size:14px}.chain-select-content [role=menuitem]:hover,.model-select-content [role=menuitem]:hover{background:var(--background-modifier-hover)}}.model-card{background:var(--background-primary-alt);border-radius:8px;padding:0 16px;box-shadow:0 2px 4px rgba(0,0,0,.1);transition:all .3s ease;position:relative;border-left:4px solid transparent}.model-card.selected{border-left-color:var(--interactive-accent)}.model-card-header{display:flex;justify-content:space-between;align-items:flex-start;padding-bottom:8px;cursor:pointer}.model-card-header-content{display:flex;align-items:baseline;flex:1;margin-right:12px}.model-provider-wrapper{display:flex;flex-direction:column;gap:3px;padding-left:10px;width:100%}.model-provider{font-size:.85em;color:var(--text-muted);padding:2px 0;background:var(--background-secondary);border-radius:4px;width:-moz-fit-content;width:fit-content;align-self:flex-start}.model-delete-icon{position:absolute;top:8px;right:8px;width:24px;height:24px;display:flex;align-items:center;justify-content:center;cursor:pointer;color:var(--text-muted);opacity:.6;transition:all .2s ease;background:transparent;border:none;padding:0}.model-delete-icon:hover{color:var(--text-error);opacity:1}.placeholder\:tw-text-sm::-moz-placeholder{font-size:.875rem;line-height:1.25rem}.placeholder\:tw-text-sm::placeholder{font-size:.875rem;line-height:1.25rem}.placeholder\:tw-text-muted::-moz-placeholder{color:var(--text-muted)}.placeholder\:tw-text-muted::placeholder{color:var(--text-muted)}.hover\:tw-cursor-grab:hover{cursor:grab}.hover\:tw-cursor-pointer:hover{cursor:var(--cursor-link)}.hover\:tw-border-interactive-accent:hover{border-color:var(--interactive-accent)}.hover\:\!tw-bg-interactive-accent:hover{background-color:var(--interactive-accent)!important}.hover\:\!tw-bg-interactive-hover:hover{background-color:var(--interactive-hover)!important}.hover\:tw-bg-dropdown-hover:hover{background-color:var(--dropdown-background-hover)}.hover\:tw-bg-interactive-accent:hover{background-color:var(--interactive-accent)}.hover\:tw-bg-interactive-accent-hover:hover{background-color:var(--interactive-accent-hover)}.hover\:tw-bg-interactive-hover:hover{background-color:var(--interactive-hover)}.hover\:tw-bg-modifier-error:hover{background-color:var(--background-modifier-error)}.hover\:tw-bg-modifier-success:hover{background-color:var(--background-modifier-success)}.hover\:tw-bg-transparent:hover{background-color:transparent}.hover\:tw-bg-opacity-100:hover{--tw-bg-opacity:1}.hover\:\!tw-text-on-accent:hover{color:var(--text-on-accent)!important}.hover\:tw-text-accent:hover{color:var(--text-accent)}.hover\:tw-text-accent-hover:hover{color:var(--text-accent-hover)}.hover\:tw-text-normal:hover{color:var(--text-normal)}.hover\:tw-text-on-accent:hover{color:var(--text-on-accent)}.hover\:tw-underline:hover{text-decoration-line:underline}.hover\:tw-shadow-\[0_2px_12px_rgba\(0\2c 0\2c 0\2c 0\.1\)\]:hover{--tw-shadow:0 2px 12px rgba(0,0,0,.1);--tw-shadow-colored:0 2px 12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.hover\:tw-shadow-md:hover{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.hover\:tw-bg-accent\/50:hover{background-color:color-mix(in srgb,var(--text-accent) 50%,transparent)}.hover\:tw-bg-primary-alt\/50:hover{background-color:color-mix(in srgb,var(--background-primary-alt) 50%,transparent)}.hover\:tw-bg-interactive-accent\/10:hover{background-color:color-mix(in srgb,var(--interactive-accent) 10%,transparent)}.focus\:tw-bg-dropdown-hover:focus{background-color:var(--dropdown-background-hover)}.focus\:tw-bg-interactive-accent:focus{background-color:var(--interactive-accent)}.focus\:tw-text-normal:focus{color:var(--text-normal)}.focus\:tw-text-on-accent:focus{color:var(--text-on-accent)}.focus\:tw-outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:tw-ring-1:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus\:tw-ring-ring:focus{--tw-ring-color:var(--interactive-accent)}.focus-visible\:tw-text-normal:focus-visible{color:var(--text-normal)}.focus-visible\:\!tw-shadow-sm:focus-visible{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05)!important;--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color)!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.focus-visible\:tw-shadow-sm:focus-visible{--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.focus-visible\:\!tw-outline-none:focus-visible{outline:2px solid transparent!important;outline-offset:2px!important}.focus-visible\:tw-outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\:\!tw-ring-1:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)!important;--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color)!important;box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)!important}.focus-visible\:tw-ring-0:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus-visible\:tw-ring-1:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus-visible\:tw-ring-2:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus-visible\:\!tw-ring-ring:focus-visible{--tw-ring-color:var(--interactive-accent)!important}.focus-visible\:tw-ring-ring:focus-visible{--tw-ring-color:var(--interactive-accent)}.focus-visible\:tw-ring-offset-2:focus-visible{--tw-ring-offset-width:2px}.active\:tw-scale-\[0\.98\]:active{--tw-scale-x:0.98;--tw-scale-y:0.98;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.active\:tw-cursor-grabbing:active{cursor:grabbing}.disabled\:tw-pointer-events-none:disabled{pointer-events:none}.disabled\:tw-cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:tw-opacity-50:disabled{opacity:.5}.tw-group:hover .group-hover\:tw-opacity-100{opacity:1}.tw-peer:focus-visible~.peer-focus-visible\:\!tw-opacity-0{opacity:0!important}.tw-peer:disabled~.peer-disabled\:tw-cursor-not-allowed{cursor:not-allowed}.tw-peer:disabled~.peer-disabled\:tw-opacity-70{opacity:.7}.data-\[disabled\]\:tw-pointer-events-none[data-disabled]{pointer-events:none}.data-\[side\=bottom\]\:tw-translate-y-1[data-side=bottom]{--tw-translate-y:var(--size-4-1)}.data-\[side\=bottom\]\:tw-translate-y-1[data-side=bottom],.data-\[side\=left\]\:-tw-translate-x-1[data-side=left]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=left\]\:-tw-translate-x-1[data-side=left]{--tw-translate-x:calc(var(--size-4-1)*-1)}.data-\[side\=right\]\:tw-translate-x-1[data-side=right]{--tw-translate-x:var(--size-4-1)}.data-\[side\=right\]\:tw-translate-x-1[data-side=right],.data-\[side\=top\]\:-tw-translate-y-1[data-side=top]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=top\]\:-tw-translate-y-1[data-side=top]{--tw-translate-y:calc(var(--size-4-1)*-1)}.data-\[state\=active\]\:\!tw-bg-interactive-accent[data-state=active],.data-\[state\=checked\]\:\!tw-bg-interactive-accent[data-state=checked]{background-color:var(--interactive-accent)!important}.data-\[state\=open\]\:tw-bg-dropdown-hover[data-state=open]{background-color:var(--dropdown-background-hover)}.data-\[state\=selected\]\:tw-bg-primary-alt[data-state=selected]{background-color:var(--background-primary-alt)}.data-\[state\=active\]\:\!tw-text-on-accent[data-state=active],.data-\[state\=checked\]\:\!tw-text-on-accent[data-state=checked]{color:var(--text-on-accent)!important}.data-\[disabled\]\:tw-opacity-50[data-disabled]{opacity:.5}.data-\[state\=active\]\:\!tw-shadow-md[data-state=active]{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1)!important;--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color)!important;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)!important}.data-\[state\=open\]\:tw-animate-in[data-state=open]{animation-name:enter;animation-duration:.15s;--tw-enter-opacity:initial;--tw-enter-scale:initial;--tw-enter-rotate:initial;--tw-enter-translate-x:initial;--tw-enter-translate-y:initial}.data-\[state\=closed\]\:tw-animate-out[data-state=closed]{animation-name:exit;animation-duration:.15s;--tw-exit-opacity:initial;--tw-exit-scale:initial;--tw-exit-rotate:initial;--tw-exit-translate-x:initial;--tw-exit-translate-y:initial}.data-\[state\=closed\]\:tw-fade-out-0[data-state=closed]{--tw-exit-opacity:0}.data-\[state\=open\]\:tw-fade-in-0[data-state=open]{--tw-enter-opacity:0}.data-\[state\=closed\]\:tw-zoom-out-95[data-state=closed]{--tw-exit-scale:.95}.data-\[state\=open\]\:tw-zoom-in-95[data-state=open]{--tw-enter-scale:.95}.data-\[side\=bottom\]\:tw-slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y:-var(--size-4-2)}.data-\[side\=left\]\:tw-slide-in-from-right-2[data-side=left]{--tw-enter-translate-x:var(--size-4-2)}.data-\[side\=right\]\:tw-slide-in-from-left-2[data-side=right]{--tw-enter-translate-x:-var(--size-4-2)}.data-\[side\=top\]\:tw-slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y:var(--size-4-2)}.data-\[state\=closed\]\:tw-slide-out-to-left-1\/2[data-state=closed]{--tw-exit-translate-x:-50%}.data-\[state\=closed\]\:tw-slide-out-to-top-\[48\%\][data-state=closed]{--tw-exit-translate-y:-48%}.data-\[state\=open\]\:tw-slide-in-from-left-1\/2[data-state=open]{--tw-enter-translate-x:-50%}.data-\[state\=open\]\:tw-slide-in-from-top-\[48\%\][data-state=open]{--tw-enter-translate-y:-48%}.hover\:data-\[state\=closed\]\:\!tw-bg-interactive-accent[data-state=closed]:hover{background-color:var(--interactive-accent)!important}.hover\:data-\[state\=closed\]\:\!tw-text-on-accent[data-state=closed]:hover{color:var(--text-on-accent)!important}@container chat-input (min-width: 20rem){.\@xs\/chat-input\:tw-inline-flex{display:inline-flex}}@container (min-width: 42rem){.\@2xl\:tw-grid{display:grid}.\@2xl\:tw-grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@container (min-width: 56rem){.\@4xl\:tw-grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (min-width:640px){.sm\:tw-flex{display:flex}.sm\:tw-w-\[200px\]{width:200px}.sm\:tw-w-\[300px\]{width:300px}.sm\:tw-w-auto{width:auto}.sm\:tw-max-w-\[425px\]{max-width:425px}.sm\:tw-max-w-\[500px\]{max-width:500px}.sm\:tw-flex-row{flex-direction:row}.sm\:tw-items-center{align-items:center}.sm\:tw-justify-start{justify-content:flex-start}.sm\:tw-justify-end{justify-content:flex-end}.sm\:tw-justify-between{justify-content:space-between}.sm\:tw-space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(var(--size-4-2)*var(--tw-space-x-reverse));margin-left:calc(var(--size-4-2)*(1 - var(--tw-space-x-reverse)))}.sm\:tw-self-auto{align-self:auto}.sm\:tw-rounded-lg{border-radius:var(--radius-l)}.sm\:tw-text-left{text-align:left}.sm\:tw-text-xs{font-size:.75rem;line-height:1rem}}@media (min-width:768px){.md\:tw-block{display:block}.md\:tw-hidden{display:none}.md\:tw-max-w-32{max-width:8rem}.md\:\!tw-text-base{font-size:1rem!important;line-height:1.5rem!important}.md\:tw-text-sm{font-size:.875rem;line-height:1.25rem}}@media (min-width:1024px){.lg\:tw-max-w-32{max-width:8rem}}.\[\&\:has\(\[role\=checkbox\]\)\]\:tw-pr-0:has([role=checkbox]){padding-right:0}.\[\&\>\[role\=checkbox\]\]\:tw-translate-y-\[2px\]>[role=checkbox]{--tw-translate-y:2px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.\[\&\>span\]\:tw-line-clamp-1>span{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}.\[\&\>svg\]\:tw-size-4>svg{width:var(--size-4-4);height:var(--size-4-4)}.\[\&\>svg\]\:tw-shrink-0>svg{flex-shrink:0}.tw-group:hover .group-hover\:\[\&\>svg\]\:tw-text-normal>svg{color:var(--text-normal)}.\[\&_svg\]\:tw-pointer-events-none svg{pointer-events:none}.\[\&_svg\]\:tw-size-4 svg{width:var(--size-4-4);height:var(--size-4-4)}.\[\&_svg\]\:tw-shrink-0 svg{flex-shrink:0}.\[\&_tr\]\:tw-border-b tr{border-bottom-width:var(--border-width)} :root {
  --toolbar-horizontal-offset: 0px;
  --toolbar-vertical-offset: 0px;
  --editing-toolbar-background-color: rgba(var(--background-secondary-rgb), 0.7);
  --editing-toolbar-icon-color: var(--text-normal);
  --toolbar-icon-size: 18px;
}


#editingToolbarModalBar {
  width: auto;
  height: auto;
  /* padding: 3px; */
  display: grid;
  user-select: none;
  border-radius: var(--radius-m);
  position: absolute;
  transition: 100ms cubic-bezier(0.92, -0.53, 0.65, 1.21);
  -webkit-transition: 100ms cubic-bezier(0.92, -0.53, 0.65, 1.21);
  min-width: fit-content;
  justify-content: space-around;
  z-index: var(--layer-modal);
  transform: translate(var(--toolbar-horizontal-offset), var(--toolbar-vertical-offset));
}
#editingToolbarModalBar.editingToolbarCustomAesthetic,
:is(#editingToolbarModalBar, #editingToolbarPopoverBar).editingToolbarCustomAesthetic button[class^=editingToolbarCommandsubItem]>.subitem,
#editingToolbarPopoverBar.editingToolbarCustomAesthetic
{
  background-color: var(--editing-toolbar-background-color);
  gap: calc(var(--toolbar-icon-size) / 15) !important;


}
#editingToolbarModalBar.editingToolbarCustomAesthetic
{
  padding:calc(var(--toolbar-icon-size) / 4);
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar).editingToolbarCustomAesthetic :is(.editingToolbarCommandItem, button[class^=editingToolbarCommandsubItem]):not(.editingToolbar-Divider-Line) {
 
height: var(--toolbar-icon-size) + 8px;
}

/* 应用到工具栏图标 */
:is(#editingToolbarModalBar, #editingToolbarPopoverBar).editingToolbarCustomAesthetic .editingToolbarCommandItem svg,
:is(#editingToolbarModalBar, #editingToolbarPopoverBar).editingToolbarCustomAesthetic button[class^=editingToolbarCommandsubItem] svg,
:is(#editingToolbarModalBar, #editingToolbarPopoverBar).editingToolbarCustomAesthetic button[class^=editingToolbarCommandsubItem]>.subitem svg
 {
  color: var(--editing-toolbar-icon-color);
  width: var(--toolbar-icon-size);
  height: var(--toolbar-icon-size);
  max-width: var(--toolbar-icon-size);
  max-height: var(--toolbar-icon-size);
}

#editingToolbarModalBar.fixed{
    display: grid;
    grid-template-columns: repeat(12, calc(var(--toolbar-icon-size) + 10px));
} 
 
 
#editingToolbarModalBar.editingToolbarFlex {
  display: flex;
  transform:none;
}

#editingToolbarModalBar.editingToolbarFlex :is(.editingToolbarCommandItem, button[class^=editingToolbarCommandsubItem]) {
  min-width: 20px;
}

#editingToolbarModalBar .editingToolbarCommandItem {
  margin: 2px;
  border: none;
  display: flex;
  cursor: default;
  padding: 5px 6px;
  box-shadow: none;
  margin-left: 3px;
  margin-right: 3px;
  position: relative;
  border-radius: var(--radius-s);
  font-size: initial !important;
  background-color: var(--background-primary-alt);
  height: auto;
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem button:hover,
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommand]:hover,
#editingToolbarSecond:hover {
  background-color: var(--background-modifier-hover) !important;
}

/* #editingToolbarModalBar button.editingToolbarCommandItem svg {
  width: 1.3em;
  height: 1.3em;
} */

/*----------------------------------------------------------------
editingToolbar SETTINGS BUTTONS
----------------------------------------------------------------*/

 

 

button.editingToolbarSettingsButton {
  padding: 4px 14px;
  border-radius: var(--radius-m);
}



/*----------------------------------------------------------------
editingToolbar SETTING ITEMS
----------------------------------------------------------------*/
.setting-item.editingToolbarCommandItem:first-child {
  padding-top: 12px;
}

.editingToolbarCommandItem {
  cursor: grab;
  padding: 10px 0 10px 0;
}

.editingToolbarSettingsTabsContainer .sortable-fallback {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.editingToolbarSettingsTabsContainer .sortable-grab {
  cursor: grabbing !important;
}

.editingToolbarSettingsTabsContainer .sortable-ghost {
  opacity: 0.4;
  cursor: grabbing;
}

.editingToolbarSettingsTabsContainer .sortable-chosen {
  cursor: grabbing;
  padding: 10px 0 10px 10px;
  background-color: var(--color-base-10, --background-primary);
}

.editingToolbarSettingsTabsContainer .sortable-drag {
  cursor: grabbing;
  box-shadow: 0px 3px 32px rgb(31 38 135 / 15%);
}

.editingToolbarSettingsTabsContainer {
  border-top: 1px solid var(--background-modifier-border);
  border-bottom: 1px solid var(--background-modifier-border);
}

/*----------------------------------------------------------------
editingToolbar CLASS CHANGES
----------------------------------------------------------------*/

#editingToolbarModalBar.editingToolbarDefaultAesthetic {
  border: 1px solid var(--background-modifier-border);
}

#editingToolbarModalBar.editingToolbarDefaultAesthetic:not(.top) :is(.editingToolbarCommandItem, button[class^=editingToolbarCommandsubItem]) {
  min-height: 28px;
}

#editingToolbarModalBar.editingToolbarDefaultAesthetic:not(.top) button[class^=editingToolbarCommandsubItem] {
  margin: auto;
  padding: 6px;
  box-shadow: none;
  border: none;
  background-color: transparent;

  place-items: center;

}

.editingToolbarDefaultAesthetic {
  background-color: var(--color-base-10, --background-primary);
}


#editingToolbarModalBar.editingToolbarGlassAesthetic,
#editingToolbarModalBar.editingToolbarGlassAesthetic~#editingToolbarPopoverBar {
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  box-shadow: none;
  background-color: transparent;
}

#editingToolbarModalBar.editingToolbarGlassAesthetic:not(.top) button[class^=editingToolbarCommandsubItem] {
  margin: auto;
  padding: 0px;
  box-shadow: none;
  border: none;
  display: flex;
  border-radius: var(--radius-s);
  font-size: 10px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;
  place-items: center;
  clear: both;
  max-width: 28px;
  max-height: 23px;
}

/*----------------------------------------------------------------
editingToolbar ICONS
----------------------------------------------------------------*/

.editingToolbarIconPick {
  line-height: normal;
  vertical-align: middle;
  margin-right: 8px;
}

.editingToolbarIconPick svg {
  width: 17px;
  height: 17px;
}

 
 

 

/*----------------------------------------------------------------
editingToolbar SUPPORT
---------------------------------------------`-------------------*/
 

#editingToolbarModalBar {
  align-items: center;
  justify-items: center;
  border: none;
  backdrop-filter: none;
}

 

#editingToolbarModalBar.editingToolbarGlassAesthetic [class^=editingToolbarCommandsubItem] {

  background-color: #ffffff00;
}

#editingToolbarModalBar .editingToolbarCommandItem {
  justify-content: center;
  align-content: center;
  place-items: center;

}


/*  #editingToolbarModalBar.editingToolbarTinyAesthetic .editingToolbarCommandItem svg{
     width: 1em;
     height: 1em;
 } */



div.modal-container.editingToolbar-Modal:not(:is(.changename,.customicon)) .modal-bg {
  background-color: transparent !important;
  backdrop-filter: none !important;
  position: relative;
}

.modal-container.editingToolbar-Modal:not(:is(.changename,.customicon)) .modal {
  padding: 10px 20px;
  width: 220px;
  position: absolute;
  bottom: 2em;
  right: 0.5em;
  background-color: rgb(var(--mono-rgb-0), 0.65);
  backdrop-filter: blur(2px);
}

.modal-container.editingToolbar-Modal .modal-title {
  display: none;
}

.modal-container.editingToolbar-Modal .modal input[type='range'] {
  width: 90%;

}

body.theme-dark .modal-container.editingToolbar-Modal .modal input[type='range'] {
  background-color: var(--background-secondary);
}

/*tiny 样式*/
#editingToolbarModalBar.editingToolbarTinyAesthetic {
  align-items: center;
  justify-items: center;
  border: 1px solid var(--background-modifier-border-hover);
  backdrop-filter: none;
  background-color: var(--background-secondary);
}


#editingToolbarModalBar.editingToolbarTinyAesthetic .editingToolbarCommandItem {
  margin: auto;
  padding: 0px;
  box-shadow: none;
  margin-left: 0px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;

}

#editingToolbarModalBar .editingToolbarCommandItem {
  margin: auto;
  padding: 0px;
  box-shadow: none;

  margin-left: 4px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;
 

}


:is(#editingToolbarModalBar).editingToolbarTinyAesthetic:not(.top) button[class^=editingToolbarCommandsubItem] {
  margin: auto;
  padding: 0px;
  box-shadow: none;
  border: none;
  display: flex;
  border-radius: var(--radius-s);
  font-size: 10px;
  margin-right: 0px;
  position: relative;
  background-color: transparent;

  place-items: center;
  clear: both;
  max-width: 28px;
  max-height: 18px;

}

button[class^=editingToolbarCommandsubItem]::after {

  content: url("data:image/svg+xml,%3Csvg width='4' height='4' version='1.1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024' enable-background='new 0 0 1024 1024' xml:space='preserve'%3E%3Cpath fill='%23666' d='M13.24 80.11 l461.75 560.8 q14.56 16.02 36.41 16.02 q21.85 0 36.42 -16.02 l463.2 -560.8 q10.2 -10.19 12.38 -24.75 q2.18 -14.57 -3.64 -27.68 q-5.82 -13.11 -18.21 -20.39 q-12.39 -7.29 -26.95 -7.29 l-924.95 0 q-20.4 0 -34.23 13.11 q-13.84 13.11 -15.29 32.77 q-1.46 19.66 13.11 34.23 Z'/%3E%3C/svg%3E");

  margin-left: 1px;
  margin-top: 6px;

}


:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem {
  background-color: var(--color-base-10, --background-primary);
  border-radius: var(--radius-s);
  border: 1px solid var(--background-modifier-border);

  position: absolute;
  z-index: var(--layer-menu);
  user-select: none;
  transform: translateY(105%) translateX(0%);
  -webkit-transform: translateY(105%) translateX(0%);
  display: flex;

}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]:first-child>.subitem
{
  left: 0;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]:nth-child(2)>.subitem
{
  left: 0;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]:nth-last-child(1)>.subitem
{
  right: 0;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]:nth-last-child(2)>.subitem
{
  right: 0;
}
@media screen and (max-width: 768px) {
  :is(#editingToolbarModalBar, #editingToolbarPopoverBar):not(:is(.top,.editingToolbarFlex)) button[class^=editingToolbarCommandsubItem]>.subitem {
    left: 58%;
    transform: translate(-60%, 90%);
  }
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem svg {
  max-width: 1.3em;
  max-height: 1.3em;
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem button.menu-item {
  background-color: transparent;
  line-height: 2em;
  display: inline-flex;
  box-shadow: none;
  align-items: center;
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem .menu-item {
  margin-left: 2px;
  margin-right: 2px;
  padding: 0px 4px 0px 4px;
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem {

  visibility: hidden;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}





:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button.editingToolbarCommandsubItem-font-color .triangle-icon {
  position: absolute;
  margin-left: 18px;
  bottom: 8%;
  background-size: 4px 4px;
  background-image: url("data:image/svg+xml,%3Csvg width='4' height='4' version='1.1' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024' enable-background='new 0 0 1024 1024' xml:space='preserve'%3E%3Cpath fill='%23666' d='M13.24 80.11 l461.75 560.8 q14.56 16.02 36.41 16.02 q21.85 0 36.42 -16.02 l463.2 -560.8 q10.2 -10.19 12.38 -24.75 q2.18 -14.57 -3.64 -27.68 q-5.82 -13.11 -18.21 -20.39 q-12.39 -7.29 -26.95 -7.29 l-924.95 0 q-20.4 0 -34.23 13.11 q-13.84 13.11 -15.29 32.77 q-1.46 19.66 13.11 34.23 Z'/%3E%3C/svg%3E");

  width: 16px;
  height: 20px;
  background-position: center;

  background-repeat: no-repeat;
  min-width: unset;
  border-left: 2px solid transparent;
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar):not(.top) button.editingToolbarCommandsubItem-font-color .triangle-icon {
  margin-left: 16px;

}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar):not(:is(.top,.editingToolbarFlex)) button.editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  top: auto;
  bottom: calc(100% - 2rem);
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar):not(:is(.top,.editingToolbarFlex)) button[class^=editingToolbarCommandsubItem]:not(.editingToolbarSecond)>.subitem {
  bottom: calc(100% + calc(var(--toolbar-icon-size) + 18px));
}


:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button.editingToolbarCommandsubItem-font-color .subitem {
  visibility: hidden;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button.editingToolbarCommandsubItem-font-color .subitem:hover {
  visibility: visible;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}


:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]:hover>.subitem {

  visibility: visible;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
}

/* :is(#editingToolbarModalBar,#editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem] >.subitem:hover  {
  visibility:visible;
  transition: all 0.3s linear;
  -webkit-transition:  all 0.3s linear;
 } */

.editingToolbarCommandsubItem-font-color button {
  background-color: transparent;
}



.editingToolbarSettingsTabsContainer .editingToolbarCommandItem .setting-item-info {

  flex: 30%;
  margin: 0;
}

.editingToolbarSettingsTabsContainer .editingToolbarSettingsTabsContainer_sub {
  border-left: 1px solid var(--background-modifier-border);
  flex-flow: column;
  min-height: 45px;
  display: flex;
  padding: 0;
  margin-left: 10px;
  flex: 70%;
}

.editingToolbarSettingsTabsContainer .editingToolbarSettingsTabsContainer_sub:empty {
  border: 2px dashed rgba(var(--interactive-accent-rgb), 0.5);
}

.editingToolbarSettingsTabsContainer .editingToolbarSettingsTabsContainer_sub:empty::before {
  content: "✖️Drag it here";
  margin: auto;
  font-size: 12px;
}

.editingToolbarSettingsTabsContainer .editingToolbarSettingsTabsContainer_sub .editingToolbarCommandItem {
  flex: auto;
  margin-left: 2em;
  ;
}

.editingToolbarSettingsTabsContainer .editingToolbarSettingsTabsContainer_sub .setting-item-control {
  flex: 0;
}

.editingToolbarSettingsTabsContainer .editingToolbarCommandSubItem>.setting-item-info {
  flex: 70px;
}

.editingToolbarCommandSubItem>.setting-item-control {

  justify-content: flex-start;
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).editingToolbarSettingsButton.editingToolbarSettingsButtonaddsub {
  background-color: var(--background-secondary-alt);
}


.setting-item button.editingToolbarSettingsIcon {
  display: block;
  transform: translateX(-30%);
  -webkit-transform: translateX(-30%);
}

.setting-item .editingToolbarSettingsIcon:empty::before {
  content: "❗";
}
.modal.mod-settings .custom-commands-container .editingToolbarSettingsIcon{
  box-shadow: none!important;
}
.modal.mod-settings .custom-commands-container .editingToolbarSettingsIcon:hover{
  background-color: transparent!important;
  color: var(--text-normal)!important;
}
.modal .setting-item-control>input.id-is-disabled{
  background-color: var(--background-modifier-border);
}
.setting-item button.editingToolbarSettingsIcon svg {
  max-width: 1.5em;
  max-height: 1.5em;
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).editingToolbarSettingsIcon:hover {
  background-color: var(--interactive-accent-hover);
}

.modal.mod-settings button:not(.mod-cta):not(.mod-warning).editingToolbarSettingsIcon {
  background-color: transparent;
  box-shadow: 0 1px 1px 0px var(--background-modifier-border);
}

@media screen and (min-width: 781px) {
  .editingToolbar-Modal .wideInputPromptInputEl {
    width: 40rem;
    max-width: 100%;
    height: 20rem;
    background-color: rgb(var(--mono-rgb-0), 0.8);
  }
}

.editingToolbarcustomIcon svg {
  max-width: 1.3em;
  max-height: 1.3em;
  display: flex;
}


.editingToolbarSettingsButton svg {
  max-width: 1.3em;
  max-height: 1.3em;
  display: flex;
}

.cmdr-page-header {
  min-width: 1em;
  ;
}


.x-color-picker-wrapper {
  right: -50%;
  transform: translateX(50%);
  top: 1.8em;
  min-width: 1px;
  padding: 10px;
  box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14);
  position: absolute;
  width: fit-content;
  font-weight: 400;
  font-family: Source Sans Pro, sans-serif;
  border-radius: var(--radius-s);
  background-color: var(--color-base-10, --background-primary);
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:first-child.editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: unset;
  transform: translateX(0);
  left: 0px;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-child(2).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: unset;
  transform: translateX(0);
  left: 0px;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-child(3).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: unset;
  transform: translateX(0);
  left: -30px;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-child(4).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: unset;
  transform: translateX(0);
  left: -50px;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-last-child(1).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: 0px;
  transform: translateX(0);
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-last-child(2).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: 0px;
  transform: translateX(0);
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-last-child(3).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: 0px;
  transform: translateX(30px);
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-last-child(4).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: 0px;
  transform: translateX(40px);
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button:nth-last-child(5).editingToolbarCommandsubItem-font-color .x-color-picker-wrapper {
  right: 0px;
  transform: translateX(50px);
}

.markdown-source-view.mod-cm6 .x-color-picker-wrapper table.x-color-picker-table#x-color-picker-table {
  width: unset;
  border-collapse: separate;
  border-spacing: 6px;
  margin: auto !important;
}

.x-color-picker-wrapper table.x-color-picker-table#x-backgroundcolor-picker-table {
  width: unset;
  border-collapse: separate;
  border-spacing: 6px;
  margin: auto !important;
}

.x-color-picker-wrapper .x-color-picker-table th {
  border: 0;
  text-align: left;
  font-weight: normal;
  background: transparent !important;
  color: #718096;
}

.x-color-picker-wrapper #x-color-picker-table td {
  font-size: 1px;
  padding: 9px;
  cursor: default;
  border: solid 1px var(--background-modifier-border);
}

.x-color-picker-wrapper #x-backgroundcolor-picker-table td {
  font-size: 1px;
  border-radius: 50%;
  padding: 9px;
  cursor: default;
  border: solid 1px var(--background-modifier-border);
}

.x-color-picker-wrapper .x-color-picker-table tr td:hover {
  filter: brightness(1.2);
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05);
}

/* .x-color-picker-wrapper tbody>tr:hover {
  background-color: transparent !important;
} */

/**top**/
#editingToolbarModalBar.top {
  display: flex;
  flex-wrap: nowrap;
  position: relative;
  height: calc(var(--toolbar-icon-size) + 18px);
  align-items: center;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  transform: none;
  z-index: var(--layer-status-bar);
}
#editingToolbarModalBar ~.excalidraw .FixedSideContainer_side_top
{
  top: 38px!important;
}
.excalidraw-wrapper #editingToolbarModalBar.top
{
  top: 38px;
}
.excalidraw-wrapper #editingToolbarPopoverBar
{
  top: 76px;
}
#editingToolbarModalBar.top.autohide {
  opacity: 0;
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
}
#editingToolbarModalBar.top.centered {
  max-width: var(--file-line-width) ;
  align-self: center;
}

#editingToolbarModalBar.top.autohide:hover {
  opacity: 1;
  transition: all 1s linear;
  -webkit-transition: all 1s linear;
}


:is(#editingToolbarModalBar, #editingToolbarPopoverBar) :is(.editingToolbarCommandItem, button[class^=editingToolbarCommandsubItem]):not(.editingToolbar-Divider-Line) {
  font-size: 10px;
  margin-right: 0px;
  clear: both;
  opacity: 1;
  flex-shrink: 0;
  height: 26px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: default;
  outline: none;
  box-shadow: none;
  border-radius: var(--radius-s);
  display: inline-flex;
  justify-content: center;
  align-items: center;
 
}

#editingToolbarModalBar ~.canvas-controls{
  top: calc(var(--size-4-2) + 50px);
}
 
/* #editingToolbarModalBar.top button.editingToolbarCommandItem:hover {
  background-color: var(--interactive-hover);
} */



#editingToolbarPopoverBar {

  padding: 0 10px;
  display: inline-flex;
  align-items: center;
  width: fit-content;
  z-index: var(--layer-popover);
  background-color: var(--color-base-10, --background-primary);
  background-clip: padding-box;
  border-radius: var(--radius-m);

  margin-left: auto;
  margin-right: 25px;
  transition: all 0.1s linear;
  -webkit-transition: all 0.1s linear;
 
  position: absolute;
  right: 0;
}

@media screen and (max-width: 768px) {
  #editingToolbarPopoverBar {
   left: 50%;
   transform: translateX(-50%);
   right: unset;
  }
}
.markdown-source-view.mod-cm6 #editingToolbarPopoverBar {
  margin-top: calc(var(--toolbar-icon-size) + 20px);
}

#editingToolbarPopoverBar :is(.editingToolbarCommandItem, button[class^=editingToolbarCommandsubItem]) {

  height: 26px;
  margin-left: 4px;
  font-size: 10px;
  margin-right: 4px;
  clear: both;
  opacity: 1;
  flex-shrink: 0;
  padding: 0;
  border: none;
  background: transparent;
  cursor: default;
  outline: none;
  box-shadow: none;
  border-radius: var(--radius-s);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;

}

#editingToolbarModalBar .more-menu {
  display: flex;
  align-items: center;
  box-shadow: none;
  margin-left: 4px;
  height: 24px;
  opacity: 0.8;
}

#editingToolbarModalBar.editingToolbarCustomAesthetic .more-menu>.editingToolbarCommandItem svg {
  width: calc(var(--toolbar-icon-size) * 0.8);
  height: calc(var(--toolbar-icon-size) * 0.8);
}


/*Divider-Line**/

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line {
  padding: 0;

  line-height: 0px;
  border-left: 200px solid rgba(var(--interactive-accent-rgb), 0.05);
  border-right: 200px solid rgba(var(--interactive-accent-rgb), 0.05);
  text-align: center;
  background: rgba(var(--interactive-accent-rgb), 0.2);
}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-control button:not(:last-child) {
  display: none;
}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-info {
  flex: 1 1 auto;
}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-control {
  justify-content: flex-start;
}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-info .setting-item-name {
  font-size: 12px;
  text-align: right;

}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-control button:last-child {
  padding: 0;
  background-color: transparent !important;
}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-control button:last-child svg {
  display: none;
}

.editingToolbarSettingsTabsContainer .editingToolbar-Divider-Line .setting-item-control button:last-child::before {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M511.674077 66.707284c-246.52265 0-446.347744 199.835328-446.347744 446.347744s199.825095 446.356954 446.347744 446.356954c246.50423 0 446.348768-199.844537 446.348768-446.356954S758.177284 66.707284 511.674077 66.707284zM744.967424 667.159826c21.8701 21.8701 21.8701 57.310264 0 79.199807-21.8701 21.851681-57.30924 21.851681-79.198783-0.019443L511.674077 592.264045 357.56007 746.359632c-21.8701 21.8701-57.30924 21.851681-79.17934-0.019443s-21.8701-57.290821 0-79.160921L432.493713 513.065262 278.379707 358.950232c-21.8701-21.86089-21.8701-57.328683 0-79.18855 21.8701-21.87931 57.30924-21.87931 79.17934 0l154.114007 154.104797 154.095587-154.104797c21.889543-21.87931 57.32766-21.87931 79.198783-0.010233 21.8701 21.8701 21.8701 57.348126 0 79.207993L590.89128 513.065262 744.967424 667.159826z' fill='%23666666'/%3E%3C/svg%3E");
}

:is(#editingToolbarModalBar:not(.top), #editingToolbarPopoverBar) button.editingToolbar-Divider-Line
{
  display: none;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button.editingToolbar-Divider-Line
{
  min-width: unset;
  flex-shrink: 0;
  display: inline-flex;
  width: 0.6px;
  background-color: var(--background-modifier-border);
  height: 22px;
  opacity: 0.8;
  margin: 0;
}
:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button[class^=editingToolbarCommandsubItem]>.subitem button.menu-item.editingToolbar-Divider-Line
{  min-width: unset;
  flex-shrink: 0;
  display: inline-flex;
  width: 0.6px;
  background-color: var(--background-modifier-border);
  height: 22px;
  opacity: 0.8;
  margin: 0;
  padding: 0;
  height: auto;

}

.theme-dark :is(#editingToolbarModalBar, #editingToolbarPopoverBar) button.editingToolbar-Divider-Line {

  background-color: #4f4f5188;

}

:is(#editingToolbarModalBar, #editingToolbarPopoverBar) button.editingToolbar-Divider-Line svg {
  display: none;
}

.workspace-tabs.mod-stacked .workspace-tab-header:not(.is-active)+.workspace-leaf #editingToolbarModalBar {
  opacity: 0;
}

:is(.cm-line, p) span[style^="background:rgba"] {
  color: var(--text-normal);
}

:is(.cm-line, p) span[style^="background:#"] {
  color: black;
}

.setting-item.toolbar-cta:after {
  content: "";
  position: absolute;
  top: -10%;
  width: 104%;
  left: -2%;
  height: 120%;
  outline: 2px solid var(--text-accent);
  border-radius: 1em;
  pointer-events: none;
}

.setting-item.toolbar-cta {
  position: relative;
}

.toolbar-pickr  .pcr-last-color,
.pickr  .pcr-button{
  background-color: var(--pcr-color);
}
.toolbar-pickr .pcr-interaction :not(:is(input.pcr-save,.pcr-result)){
  display:none;
}
.toolbar-pickr .pcr-swatches {
  display:none;
}
.toolbar-pickr {
  display:flex;
}


div[data-type="thino_view"] .memo-editor-wrapper:has(#editingToolbarModalBar)
{
    padding-top:0;
    padding-left:0;
    padding-right:0;
}

div[data-type="thino_view"] .memo-editor-wrapper:has(#editingToolbarModalBar) .common-tools-wrapper
{
   padding-left:16px;
    padding-right:16px;
}
div[data-type="thino_view"] .memo-editor-wrapper #editingToolbarModalBar ~ .cm-editor{
    padding-top:38px;
    padding-bottom:0px;
    padding-left:16px;
    padding-right:16px;
}
div[data-type=thino_view] .common-editor-wrapper .common-editor-inputer:has(#editingToolbarModalBar)
{
  min-height:118px;
}

div[data-type=thino_view] #editingToolbarModalBar.top
{
  position:absolute!important;
  width: 100%;
}
body.auto-hide-header .workspace-tab-header-container:hover + .workspace-tab-container .workspace-leaf .workspace-leaf-content>.view-header {
  margin-top: 0;
  transition: all 0.1s linear;
}

body.auto-hide-header .workspace-tab-header-container + .workspace-tab-container .workspace-leaf .workspace-leaf-content>.view-header:hover {
  margin-top: 0;
  transition: all 0.6s linear;
}
body.auto-hide-header .workspace-tab-header-container + .workspace-tab-container .workspace-leaf .workspace-leaf-content>.view-header {
  margin-top: -40px;
  transition: all 0.6s linear;
}
 
.setting-item:is(.custom_bg, .custom_font) .pickr-container {
  display: flex;
  gap: 8px;
  position: relative;
}

.setting-item:is(.custom_bg, .custom_font) .pickr-container .picker {
  width: 32px;
  height: 32px;
}

@media screen and (max-width: 1024px) {
  .editing-toolbar-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
/* 头部容器样式 */
.editing-toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1em;
  padding: 0.5em 0;
}

/* 标题容器样式 */
.editing-toolbar-title-container {
  flex-shrink: 0;
}

.editing-toolbar-title {
  margin: 0;
}

/* 信息容器样式 */
.editing-toolbar-info {
  display: flex;
  align-items: center;
  gap: 1em;
  flex-wrap: nowrap;
}

/* 修复按钮容器样式 */
.editing-toolbar-fix-button {
  margin: 0;
  padding: 0;
  border: 0;
}

.editing-toolbar-fix-button .setting-item-control {
  padding: 0;
}

/* 链接样式 */
.editing-toolbar-info a {
  color: var(--text-accent);
  text-decoration: none;
}

.editing-toolbar-info a:hover {
  text-decoration: underline;
}

.editing-toolbar-tabs {
  display: flex;
  border-bottom: 2px solid var(--background-modifier-border);
  margin-bottom: 24px;
  padding: 0 8px;
}

.editing-toolbar-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: -2px;
  color: var(--text-muted);
}

.editing-toolbar-tab:hover {
  color: var(--text-normal);
  background-color: var(--background-modifier-hover);
}

.editing-toolbar-tab.active {
  color: var(--text-accent);
  border-bottom-color: var(--text-accent);
}

.editing-toolbar-content {
  padding: 0 8px;
}

.editing-toolbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 8px;
}

/* 添加到你的 styles.css 文件中 */
@keyframes sortable-feedback {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

.sortable-chosen-feedback {
  animation: sortable-feedback 0.3s ease-in-out;
  position: relative;
}

.sortable-chosen-feedback::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-modifier-hover);
  opacity: 0;
  animation: toolbar-fade-in 0.3s ease-in-out forwards;
  pointer-events: none;
  border-radius: 4px;
}

@keyframes toolbar-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.3;
  }
}



.insert-callout-modal textarea {
  width: 100%;
  resize: vertical;
}

.callout-type-container {
  display: flex;
  align-items: center;
  gap: 8px;
}
.callout-type-container .setting-item
{
  width: 100%;
}
.callout-icon-container {
  display: flex;
  width: 24px;
  height: 24px;
  align-items: center;
}

.callout-icon-container svg {
  width: 16px;
  height: 16px;
  color: rgb(var(--callout-color));
}
.insert-link-modal .setting-item
{
 border-top: none;
}
.insert-link-modal   input{
     width: 100%;
}
.modal .insert-link-modal-title{
  font-size: 12px;
  color: var(--text-muted);
}
.insert-link-modal .preview-setting 
{
  display: block;
}
.insert-link-modal .preview-setting input{
  width: 100%;
  background-color: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 12px;
}
 

 .position-style-info{
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  position: sticky;
  color: white;
  background-color: rgba(var(--color-purple-rgb), 0.8);
  top: 0;
 }
 .position-style-info.following{
  background-color: rgba(var(--color-green-rgb), 0.8);
 }
 .position-style-info.fixed{
  background-color: rgba(var(--color-blue-rgb), 0.8);
 }
 .position-style-info.top{
  background-color: rgba(var(--color-yellow-rgb), 0.8);
 }
 
 .command-lists-container.following{
  background-color: rgba(var(--color-green-rgb), 0.1);
  border: 1px solid rgba(var(--color-green-rgb), 0.3);
 }
 .command-lists-container.fixed{
  background-color: rgba(var(--color-blue-rgb), 0.1);
  border: 1px solid rgba(var(--color-blue-rgb), 0.3);
 }
 .command-lists-container.top{
  background-color: rgba(var(--color-yellow-rgb), 0.1);
  border: 1px solid rgba(var(--color-yellow-rgb), 0.3);
 }
 


 @media screen and (max-width: 1024px) {
  .editing-toolbar-tabs .editing-toolbar-tab:not(.active)>span {
    display: none;
  }
 }


 
/* 命令类型标签 */
.command-type-badge {
  display: inline-block;
  padding: 2px 6px;
  margin-left: 8px;
  border-radius: 4px;
  font-size: 0.8em;
  background-color: var(--background-modifier-border);
}

.command-type-badge.regex {
  background-color: var(--text-accent);
  color: var(--text-on-accent);
} 
.custom-commands-container .command-list-container .setting-item-name {
  display: flex;
  gap: 8px;
}
.custom-commands-modal {
 
  width: auto;
  max-width: 800px;
 
}
.custom-commands-modal input {
 
 width: 100%;
 max-width: 300px;
}

.command-buttons-container {
  background: var(--background-secondary);
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 15px;
}

.command-buttons-container .setting-item {
  border: none;
  padding: 8px 0;
}

.command-buttons-container .setting-item-control {
  display: flex;
  gap: 10px;
  align-items: center;
}
 
/* 工具栏预览样式 */
.toolbar-preview {
  background-color: var(--editing-toolbar-background-color);
}

.toolbar-preview .clickable-icon svg {
  color: var(--editing-toolbar-icon-color);
  width: var(--toolbar-icon-size);
  height: var(--toolbar-icon-size);
}


 .toolbar-preview-container #editingToolbarModalBar{
  visibility:visible !important;
  position: relative;
 }
 
 
 .confirm-modal .confirm-modal-buttons {
  gap: 8px;
  display: flex;
  justify-content: flex-end;
}
.confirm-modal p{
  margin: 0;
  line-height: 1.6;
}
.format-brush-cursor .cm-content {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024' width='18' height='18'%3E%3Cpath d='M786.285714 56.888889h121.93727A20.280889 20.280889 0 0 0 928.507937 36.591746v-16.294603A20.284952 20.284952 0 0 0 908.222984 0h-398.287238A20.280889 20.280889 0 0 0 489.650794 20.297143v16.294603A20.284952 20.284952 0 0 0 509.935746 56.888889H635.936508v910.222222h-126.000762A20.280889 20.280889 0 0 0 489.650794 987.408254v16.294603a20.284952 20.284952 0 0 0 20.284952 20.297143h398.287238a20.280889 20.280889 0 0 0 20.284953-20.297143v-16.294603a20.284952 20.284952 0 0 0-20.284953-20.297143H786.285714V56.888889zM359.619048 903.233016H310.857143V690.793651H266.15873v213.577143H217.396825V690.793651H172.698413v213.577143h-29.415619c-49.290159 0-42.573206-44.901587-42.573207-44.901588S97.076825 722.306032 97.10527 642.031746H480.467302c-0.008127 80.428698-3.604317 216.299683-3.604318 216.299683s6.716952 44.901587-42.57727 44.901587H404.31746V690.793651H359.619048v212.439365zM97.828571 597.333333l0.065016-1.235301c2.816-50.704254-5.640127-98.588444 66.186159-123.936508 71.830349-25.35619 47.896381-35.210159 54.934349-83.098413 7.037968-47.88419-18.310095-71.826286-18.310095-207.034921 0-109.04381 51.301587-125.456254 79.766349-127.723682 0.353524-0.934603 0.723302-1.414095 1.113397-1.414095 16.420571 0 95.288889-7.200508 95.288889 128 0 135.208635-25.348063 159.15073-18.314159 207.03492 7.037968 47.888254-16.891937 57.742222 54.938413 83.098413 71.822222 25.348063 63.370159 73.236317 66.186159 123.936508l0.121904 2.373079H97.824508zM288.507937 166.603175a34.539683 34.539683 0 1 0 0-69.079365 34.539683 34.539683 0 0 0 0 69.079365z' fill='%235c5c5c'/%3E%3C/svg%3E") 16 16, auto;
}
.format-brush-cursor button[aria-label="Format Brush"]
{
    background-color: var(--background-modifier-hover) !important ;
}

.theme-dark.format-brush-cursor .cm-content {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1024 1024' width='18' height='18'%3E%3Cpath d='M786.285714 56.888889h121.93727A20.280889 20.280889 0 0 0 928.507937 36.591746v-16.294603A20.284952 20.284952 0 0 0 908.222984 0h-398.287238A20.280889 20.280889 0 0 0 489.650794 20.297143v16.294603A20.284952 20.284952 0 0 0 509.935746 56.888889H635.936508v910.222222h-126.000762A20.280889 20.280889 0 0 0 489.650794 987.408254v16.294603a20.284952 20.284952 0 0 0 20.284952 20.297143h398.287238a20.280889 20.280889 0 0 0 20.284953-20.297143v-16.294603a20.284952 20.284952 0 0 0-20.284953-20.297143H786.285714V56.888889zM359.619048 903.233016H310.857143V690.793651H266.15873v213.577143H217.396825V690.793651H172.698413v213.577143h-29.415619c-49.290159 0-42.573206-44.901587-42.573207-44.901588S97.076825 722.306032 97.10527 642.031746H480.467302c-0.008127 80.428698-3.604317 216.299683-3.604318 216.299683s6.716952 44.901587-42.57727 44.901587H404.31746V690.793651H359.619048v212.439365zM97.828571 597.333333l0.065016-1.235301c2.816-50.704254-5.640127-98.588444 66.186159-123.936508 71.830349-25.35619 47.896381-35.210159 54.934349-83.098413 7.037968-47.88419-18.310095-71.826286-18.310095-207.034921 0-109.04381 51.301587-125.456254 79.766349-127.723682 0.353524-0.934603 0.723302-1.414095 1.113397-1.414095 16.420571 0 95.288889-7.200508 95.288889 128 0 135.208635-25.348063 159.15073-18.314159 207.03492 7.037968 47.888254-16.891937 57.742222 54.938413 83.098413 71.822222 25.348063 63.370159 73.236317 66.186159 123.936508l0.121904 2.373079H97.824508zM288.507937 166.603175a34.539683 34.539683 0 1 0 0-69.079365 34.539683 34.539683 0 0 0 0 69.079365z' fill='%23dadada'/%3E%3C/svg%3E") 16 16, auto;
} .note-refactor-filename .setting-item-info {
    margin-right: 0;
}

.note-refactor-filename .setting-item-name {
    padding-top: 10px;
} .hints-status-bar-icon {
    margin-right: 5px;
}

.hints-status-bar-error {
    /*noinspection CssUnresolvedCustomProperty*/
    color: var(--text-error);
}

.hints-status-bar-hidden {
    display: none;
}

.hints-auth-modal-loader {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: center;
    margin: 40px 0;
}
.hints-auth-modal-loader > i {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: spin-animation 0.8s infinite linear;
}
.hints-auth-modal-loader > span {
    margin-left: 7px;
}

.hints-auth-modal-header {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin: 0;
}
.hints-auth-modal-header > i {
    margin-right: 7px;
    margin-top: 2px;
    /*noinspection CssUnresolvedCustomProperty*/
    color: var(--text-success);
}
.hints-auth-modal-button {
    margin-top: 7px;
    padding: 18px 30px;
}

.hints-settings-has-error .setting-item-control input {
    /*noinspection CssUnresolvedCustomProperty*/
    border-color: var(--background-modifier-error);
}

@keyframes spin-animation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
 .smtcmp-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--size-4-2);
}

.smtcmp-chat-header-title {
  font-size: var(--font-ui-medium);
  font-weight: var(--font-medium);
  margin: 0;
}

.smtcmp-chat-header-buttons {
  display: flex;
  gap: var(--size-2-1);

  svg {
    height: var(--icon-l);
    width: var(--icon-l);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-markdown {
  line-height: var(--line-height-normal);
  font-size: var(--font-ui-small);

  h1 {
    font-size: var(--font-ui-large);
  }

  h2 {
    font-size: var(--font-ui-medium);
  }

  h3 {
    font-size: var(--font-ui-small);
  }

  h4 {
    font-size: var(--font-ui-smaller);
  }

  h5 {
    font-size: var(--font-ui-smallest);
  }

  h6 {
    font-size: var(--font-ui-smallest);
  }

  p {
    font-size: var(--font-ui-small);
  }

  ul {
    font-size: var(--font-ui-small);
    padding-left: var(--size-4-4);
  }

  ol {
    font-size: var(--font-ui-small);
    padding-left: var(--size-4-4);
  }

  li {
    font-size: var(--font-ui-small);
  }

  blockquote {
    font-size: var(--font-ui-small);
    font-style: var(--blockquote-style);
    background-color: var(--blockquote-background-color);
    margin: 0;
    padding-left: var(--size-4-2);
    border-left: var(--blockquote-border-thickness) solid
      var(--blockquote-border-color);
  }

  code {
    font-size: var(--font-ui-small);
    border-radius: var(--code-radius);
    padding: 0.1em 0.25em;
    color: var(--code-normal);
    font-size: var(--code-size);
    background-color: var(--code-background);
    vertical-align: baseline;
  }

  table {
    font-size: var(--font-ui-small);
  }

  thead {
    font-size: var(--font-ui-small);
  }

  tbody {
    font-size: var(--font-ui-small);
  }

  tr {
    font-size: var(--font-ui-small);
  }

  td {
    font-size: var(--font-ui-small);
  }

  th {
    font-size: var(--font-ui-small);
  }
}

.smtcmp-chat-container {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;

  .smtcmp-stop-gen-btn {
    z-index: 1000;
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: var(--size-4-1);
  }
}

.smtcmp-chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  user-select: text;
  display: flex;
  flex-direction: column;
  gap: var(--size-4-1);
  padding: var(--size-4-1);
  margin: calc(var(--size-4-1) * -1);

  .smtcmp-chat-messages-user {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }

  .smtcmp-chat-messages-assistant {
    display: flex;
    flex-direction: column;
  }

  .smtcmp-assistant-tool-message-group {
    padding-bottom: var(--size-4-2);
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }
}

.obsidian-default-textarea {
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  color: var(--text-normal);
  font-family: inherit;
  padding: 0;
  font-size: var(--font-ui-small);
  outline: none;
  min-height: 80px;
  max-height: 200px;
  overflow-y: auto;
  font-size: var(--font-ui-small);
  padding: var(--size-2-1);
}

@media (hover: hover) {
  .obsidian-default-textarea:hover {
    border-color: var(--background-modifier-border-hover);
    transition:
      box-shadow 0.15s ease-in-out,
      border 0.15s ease-in-out;
  }
}
.obsidian-default-textarea:active {
  border-color: var(--background-modifier-border-focus);
  transition:
    box-shadow 0.15s ease-in-out,
    border 0.15s ease-in-out;
}

/* .obsidian-default-textarea::placeholder {
  color: var(--text-faint);
} */
.obsidian-default-textarea {
  line-height: var(--line-height-tight);
}

.smtcmp-chat-user-input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  padding: calc(var(--size-2-3) + 1px);
  font-size: var(--font-ui-small);
  border-radius: var(--radius-s);
  outline: none;

  &:focus-within,
  &:focus,
  &:focus-visible,
  &:active {
    box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
    transition: box-shadow 0.15s ease-in-out;
  }
}

.smtcmp-chat-user-input-files {
  display: flex;
  flex-direction: row;
  gap: var(--size-4-1);
  flex-wrap: wrap;
  padding-bottom: var(--size-4-1);
}

.smtcmp-chat-user-input-controls {
  display: flex;
  flex-direction: row;
  gap: var(--size-4-1);
  justify-content: space-between;
  align-items: center;
  height: var(--size-4-4);

  .smtcmp-chat-user-input-controls__model-select-container {
    flex-shrink: 1;
    overflow: hidden;
  }

  .smtcmp-chat-user-input-controls__buttons {
    flex-shrink: 0;
    display: flex;
    gap: var(--size-4-2);
    align-items: center;
  }
}

.smtcmp-chat-user-input-controls .smtcmp-chat-user-input-submit-button {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  font-size: var(--font-smallest);
  color: var(--text-muted);
  padding: 0 var(--size-2-1);
  cursor: pointer;
  transition: all 0.1s ease-in-out;

  &:hover {
    color: var(--text-normal);
  }

  .smtcmp-chat-user-input-submit-button-icons {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.smtcmp-chat-user-input-file-badge {
  display: flex;
  align-items: center;
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  font-size: var(--font-smallest);
  padding: var(--size-2-1) var(--size-4-1);
  gap: var(--size-2-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &.smtcmp-chat-user-input-file-badge-focused {
    border: 1px solid var(--interactive-accent);
  }

  .smtcmp-excluded-content {
    text-decoration: line-through;
    font-style: italic;
  }

  svg {
    flex-shrink: 0;
  }
}

.smtcmp-chat-user-input-file-badge:hover {
  background-color: var(--background-modifier-hover);
}

.smtcmp-chat-user-input-file-badge-delete {
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--text-muted);
}

.smtcmp-chat-user-input-file-badge-eye {
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 var(--size-2-1) 0 var(--size-4-1);
}

.smtcmp-chat-user-input-file-badge-name {
  display: flex;
  flex-direction: row;
  gap: var(--size-2-1);
  flex-grow: 1;
  overflow: hidden;
  align-items: center;
}

.smtcmp-chat-user-input-file-badge-name-icon {
  color: var(--text-muted);
}

.smtcmp-chat-user-input-file-badge-name span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.smtcmp-chat-user-input-file-badge-name-suffix {
  color: var(--text-faint);
  flex-grow: 0;
}

.smtcmp-chat-user-input-file-badge-current {
  color: var(--color-base-50);
}

.smtcmp-chat-user-input-file-content-preview {
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);
  border: 1px solid var(--background-modifier-border);
  max-height: 350px;
  overflow-y: auto;
  padding: 0 var(--size-4-2);

  img {
    max-width: 100%;
    max-height: 350px;
  }
}

/**
 * ChatUserInput
 */

.smtcmp-lexical-content-editable-root .mention {
  background-color: var(--tag-background);
  color: var(--tag-color);
  padding: var(--size-2-1) calc(var(--size-2-1));
  border-radius: var(--radius-s);
  background-color: var(--tag-background);
  color: var(--tag-color);
  padding: 0 calc(var(--size-2-1));
  border-radius: var(--radius-s);
  word-break: break-all;
}

.smtcmp-lexical-content-editable-paragraph {
  margin: 0;
  line-height: 1.6;
}

.smtcmp-popover {
  z-index: 1000;
  background: var(--background-primary);
  box-shadow: var(--shadow-s);
  border-radius: var(--radius-m);
  border: 1px solid var(--background-modifier-border);
  overflow: hidden;
}

.smtcmp-popover ul {
  padding: 0;
  list-style: none;
  margin: 0;
  max-height: 200px;
  overflow-y: scroll;
}

.smtcmp-popover ul {
  padding: var(--size-4-1) 0;
}

.smtcmp-popover ul li {
  margin: 0;
  min-width: 180px;
  font-size: var(--font-ui-smaller);
  outline: none;
  cursor: pointer;
  border-radius: 0;
}

.smtcmp-popover ul li.selected {
  background: var(--background-modifier-hover);
}

.smtcmp-popover li {
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  margin: 0 8px 0 8px;
  padding: var(--size-2-3) var(--size-4-2);
  color: var(--text-normal);
  cursor: pointer;
  line-height: var(--line-height-tight);
  font-size: var(--font-ui-smaller);
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-shrink: 0;
  background-color: var(--background-primary);
  border-radius: 8px;
  border: 0;
  min-height: 20px;
  align-items: center;
  gap: var(--size-4-1);
  align-items: start;
}

.smtcmp-popover li.active {
  display: flex;
  width: 20px;
  height: 20px;
  background-size: contain;
}

.smtcmp-popover li:hover {
  background-color: var(--background-modifier-hover);
}

.smtcmp-popover-item-icon {
  display: flex;
  user-select: none;
  line-height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  height: 14px;
  padding-top: 1px;
  align-items: center;
  color: var(--text-muted);
  min-width: fit-content;
}

.smtcmp-popover li:hover {
  background-color: var(--background-modifier-hover);
  cursor: pointer;
}
.smtcmp-popover li .smtcmp-chat-list-dropdown-item-icon {
  visibility: hidden;
}
.smtcmp-popover li:hover .smtcmp-chat-list-dropdown-item-icon {
  visibility: visible;
}
.smtcmp-popover li .smtcmp-chat-list-dropdown-item-icon:hover {
  background-color: var(--background-modifier-hover);
  border-radius: var(--radius-s);
}

.smtcmp-chat-list-dropdown-empty {
  background: transparent;
  cursor: default;
  color: var(--text-faint);
}

.smtcmp-chat-list-dropdown-content {
  width: 280px;
  max-width: 280px;
}

.smtcmp-chat-list-dropdown-content li {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.smtcmp-chat-list-dropdown-item-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input[type='text'].smtcmp-chat-list-dropdown-item-title-input {
  width: 100%;
  font-size: var(--font-ui-smaller);
}

.smtcmp-chat-list-dropdown-item-actions {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);

  svg {
    height: var(--icon-xs);
    width: var(--icon-xs);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-code-block {
  position: relative;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
}

.smtcmp-code-block code {
  padding: 0;
}

.smtcmp-code-block-header {
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-smallest);
  display: flex;
  border-bottom: 1px solid var(--background-modifier-border);
  background-color: var(--background-primary);
  border-radius: var(--radius-s) var(--radius-s) 0 0;
  height: calc(var(--size-4-8) - var(--size-4-1));
}

.smtcmp-code-block-header-filename {
  padding-left: var(--size-4-2);
  font-size: var(--font-medium);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.smtcmp-code-block-header-button-container {
  margin-left: auto;
  min-width: fit-content;
  height: 100%;
  display: flex;
  gap: 0;
  overflow: hidden;

  .smtcmp-code-block-header-button {
    height: 100%;
    padding: 0 var(--size-4-2);
    display: flex;
    gap: var(--size-4-1);
    font-size: var(--font-small);
  }

  /* Override clickable-icon color */
  .smtcmp-code-block-header-button.clickable-icon {
    color: var(--text-color) !important;
  }
}

.smtcmp-code-block-content {
  margin: 0;
}

.smtcmp-code-block-obsidian-markdown {
  padding: var(--size-4-3);
}

#smtcmp-apply-view {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  font-family: var(--font-interface);

  --smtcmp-current-color-rgb: 185, 28, 28; /* red-700 */
  --smtcmp-incoming-color-rgb: 4, 120, 87; /* emerald-700 */

  .view-content {
    padding: 0;
  }

  .markdown-source-view.mod-cm6 {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .cm-editor {
    flex: 1 1 0;
    min-height: 0;
    position: relative !important;
    box-sizing: border-box;
    display: flex !important;
    flex-direction: column;
  }

  .cm-scroller {
    padding: var(--file-margins);
    display: flex !important;
    align-items: flex-start !important;
    line-height: 1.4;
    height: 100%;
    overflow-x: auto;
    position: relative;
    z-index: 0;
  }

  .cm-sizer {
    max-width: var(--file-line-width);
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-bottom: 488px;
  }

  .view-header {
    height: var(--header-height);
    display: flex;
    border-bottom: var(--file-header-border);
    background-color: var(--background-primary);
    z-index: 1;
    position: relative;
    gap: var(--size-4-2);
    padding: 0 var(--size-4-3);
  }

  .view-header-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
  }

  .view-actions {
    button {
      color: var(--text-normal);
      font-weight: var(--font-medium);
      gap: 2px;
    }
  }

  .smtcmp-diff-block {
    white-space: pre-wrap;
    word-break: break-word;
    display: flex;
    align-items: start;
    gap: var(--size-4-1);
    color: var(--text-normal);
    line-height: var(--line-height-normal);
  }

  .smtcmp-diff-block.added {
    background-color: rgba(var(--smtcmp-incoming-color-rgb), 0.3);
  }

  .smtcmp-diff-block.removed {
    background-color: rgba(var(--smtcmp-current-color-rgb), 0.3);
  }

  .smtcmp-diff-block-container {
    position: relative;
    scroll-margin-top: var(--size-4-8);
  }

  .smtcmp-diff-block-actions {
    position: absolute;
    right: 0;
    top: 0;
    transform: translateY(-100%);
    display: flex;
    gap: 0;
    border-radius: var(--radius-s) var(--radius-s) 0 0;
    overflow: hidden;

    button {
      padding: var(--size-4-1) var(--size-4-2);
      font-size: var(--font-ui-smaller);
      border-radius: 0;
      height: fit-content;
    }
  }

  .smtcmp-accept {
    color: white;
    background: rgba(var(--smtcmp-incoming-color-rgb), 0.8);
    &:hover {
      background: rgba(var(--smtcmp-incoming-color-rgb), 1);
    }
  }

  .smtcmp-exclude {
    color: white;
    background: rgba(var(--smtcmp-current-color-rgb), 0.8);
    &:hover {
      background: rgba(var(--smtcmp-current-color-rgb), 1);
    }
  }

  .smtcmp-diff-navigation {
    display: flex;
    align-items: center;
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

button.smtcmp-chat-input-model-select {
  background-color: transparent !important;
  box-shadow: none !important;
  border: 0 !important;
  padding: 0 !important;
  font-size: var(--font-smallest) !important;
  font-weight: var(--font-medium) !important;
  color: var(--text-muted) !important;
  cursor: pointer !important;
  height: auto !important;

  &:hover {
    color: var(--text-normal) !important;
  }

  .smtcmp-chat-input-model-select__model-name {
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .smtcmp-chat-input-model-select__icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.smtcmp-query-progress {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

.smtcmp-query-progress-detail {
  font-size: var(--font-smallest);
  color: var(--text-faint);
}

.smtcmp-dot-loader {
  display: inline-block;
  text-align: left;
}

.smtcmp-dot-loader::after {
  content: '...';
  animation: dotFade 0.75s steps(4, end) infinite;
  color: var(--text-muted);
}

@keyframes dotFade {
  0%,
  100% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
}

.smtcmp-tooltip-content {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-smallest);
  animation: fadeIn 0.1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.smtcmp-settings-textarea {
  display: block;
  border-top: none;
  padding: 0;

  .smtcmp-item-control {
    width: 100%;
  }

  .setting-item-control {
    padding-bottom: var(--size-4-3);
  }

  textarea {
    width: 100%;
    min-height: 100px;
    resize: none;
  }
}

/* prevent setting-item:first-child overwriting padding-top and border-top */
.smtcmp-settings-textarea-header {
  padding-top: 0.75em !important;
  border-top: 1px solid var(--background-modifier-border) !important;
}

.smtcmp-settings-model-container {
  margin: var(--size-4-2) 0;
  padding: var(--size-4-2) var(--size-4-4);
  border-left: 2px solid var(--interactive-accent);
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);

  .setting-item {
    border-top: none;

    &:first-child {
      margin-top: var(--size-4-2);
    }
  }
}

.smtcmp-dialog-content {
  position: fixed;
  left: calc(50% - var(--size-4-4));
  top: 50%;
  z-index: 50;
  display: grid;
  width: calc(100% - var(--size-4-8));
  max-width: 32rem;
  transform: translate(-50%, -50%);
  gap: var(--size-4-2);
  border: var(--border-width) solid var(--background-modifier-border);
  background-color: var(--background-secondary);
  padding: var(--size-4-5);
  transition-duration: 200ms;
  border-radius: var(--radius-m);
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  margin: var(--size-4-4);

  .smtcmp-dialog-header {
    margin-bottom: var(--size-4-2);
    display: grid;
    gap: var(--size-2-3);
  }

  .smtcmp-dialog-title {
    font-size: var(--font-ui-medium);
    font-weight: var(--font-semibold);
    line-height: var(--line-height-tight);
    margin: 0;
  }

  .smtcmp-dialog-input {
    display: grid;
    gap: var(--size-4-1);

    & label {
      font-size: var(--font-ui-smaller);
    }
  }

  .smtcmp-dialog-description {
    font-size: var(--font-ui-small);
    color: var(--text-muted);
    margin: 0;
  }

  .smtcmp-dialog-footer {
    margin-top: var(--size-4-2);
    display: flex;
    justify-content: flex-end;
  }

  .smtcmp-dialog-close {
    position: absolute;
    right: var(--size-4-4);
    top: var(--size-4-4);
    cursor: var(--cursor);
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.smtcmp-template-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--size-4-1);
  width: 100%;

  .smtcmp-template-menu-item-delete {
    display: flex;
    align-items: center;
    padding: var(--size-4-1);
    margin: calc(var(--size-4-1) * -1);
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.smtcmp-assistant-message-actions {
  display: flex;
  align-items: center;
  justify-content: end;

  /* button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    width: 20px;
    padding: 0;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;
    color: var(--text-faint);
    cursor: pointer;

    &:hover {
      background-color: var(--background-modifier-hover);
    }
  }

  .smtcmp-assistant-message-actions-icon--copied {
    color: var(--text-muted);
  } */
}

.smtcmp-popover-content {
  z-index: 1000;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  padding: var(--size-4-2);
  font-size: var(--font-smallest);
  animation: fadeIn 0.1s ease-in-out;
}

.smtcmp-similarity-search-results {
  display: flex;
  flex-direction: column;
  font-size: var(--font-smaller);
  padding-top: var(--size-4-1);
  padding-bottom: var(--size-4-1);
  user-select: none;

  .smtcmp-similarity-search-results__trigger {
    display: flex;
    align-items: center;
    gap: var(--size-4-1);
    padding: var(--size-4-1);
    border-radius: var(--radius-s);
    cursor: pointer;
    &:hover {
      background-color: var(--background-modifier-hover);
    }
  }

  .smtcmp-similarity-search-item {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: var(--size-4-2);
    padding: var(--size-4-1);
    border-radius: var(--radius-s);
    cursor: pointer;

    &:hover {
      background-color: var(--background-modifier-hover);
    }

    .smtcmp-similarity-search-item__similarity {
      flex-shrink: 0;
      font-size: var(--font-smallest);
      color: var(--text-muted);
    }

    .smtcmp-similarity-search-item__path {
      flex-shrink: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: var(--font-smallest);
    }

    .smtcmp-similarity-search-item__line-numbers {
      flex-shrink: 0;
      margin-left: auto;
      font-size: var(--font-smallest);
    }
  }
}

.smtcmp-llm-info-content {
  width: 320px;
  display: grid;
  gap: var(--size-4-3);
}

.smtcmp-llm-info-header {
  display: grid;
  gap: var(--size-2-2);
  font-size: var(--font-ui-small);
  font-weight: var(--font-semibold);
}

.smtcmp-llm-info-tokens {
  display: grid;
  gap: var(--size-4-2);
}

.smtcmp-llm-info-tokens-header {
  font-size: var(--font-ui-small);
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-tokens-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: var(--size-4-5);
  row-gap: var(--size-4-2);
  font-size: var(--font-ui-small);
}

.smtcmp-llm-info-token-row {
  display: flex;
  align-items: center;
  gap: var(--size-2-3);
}

.smtcmp-llm-info-token-value {
  margin-left: auto;
  color: var(--text-muted);
}

.smtcmp-llm-info-token-total {
  grid-column: span 2;
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-footer-row {
  display: flex;
  align-items: center;
  gap: var(--size-2-3);
  font-size: var(--font-ui-small);
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-footer-value {
  margin-left: auto;
}

.smtcmp-llm-info-model {
  color: var(--text-muted);
}

.smtcmp-llm-info-icon--input {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--color-green);
}

.smtcmp-llm-info-icon--output {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--color-blue);
}

.smtcmp-llm-info-icon--total {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--text-normal);
}

.smtcmp-llm-info-icon--footer {
  height: var(--size-4-4);
  width: var(--size-4-4);
}

/* Settings */

.smtcmp-settings-support-smart-composer {
  border-top: none;
}

.smtcmp-settings-section:not(:first-child) {
  margin-top: var(--size-4-10);
}

.smtcmp-settings-header {
  color: var(--text-normal);
  font-size: var(--h1-size);
  line-height: var(--line-height-tight);
  font-weight: var(--h1-weight);
  margin: var(--size-4-2) 0;
}

.smtcmp-settings-sub-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.smtcmp-settings-sub-header {
  color: var(--text-normal);
  font-size: var(--h4-size);
  line-height: var(--line-height-tight);
  font-weight: var(--h4-weight);
  margin: var(--size-4-3) 0;
}

.smtcmp-settings-desc {
  color: var(--text-muted);
  font-size: var(--font-ui-small);
  line-height: var(--line-height-tight);
  margin: var(--size-4-1) 0;
}

.smtcmp-settings-callout {
  padding-inline-start: var(--size-4-2);
  border-inline-start: var(--blockquote-border-thickness) solid
    var(--blockquote-border-color);
}

.smtcmp-settings-required::after {
  color: var(--color-red);
  content: '*';
  display: inline-block;
  font-size: var(--font-ui-medium);
  font-weight: var(--font-bold);
  margin-left: var(--size-4-1);
}

/* Settings: Embedding DB Manage */

.smtcmp-settings-embedding-db-manage-root {
  padding: var(--size-4-2);
}

.smtcmp-settings-embedding-db-manage-header {
  display: flex;
  align-items: center;
  gap: var(--size-4-2);
  font-size: var(--font-ui-small);
}

.smtcmp-settings-embedding-db-manage-table {
  width: 100%;
  border-collapse: collapse;
}

.smtcmp-settings-embedding-db-manage-table tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-embedding-db-manage-table th,
.smtcmp-settings-embedding-db-manage-table td {
  padding: var(--size-4-2);
  text-align: left;
}

.smtcmp-settings-embedding-db-manage-table th {
  font-weight: var(--font-medium);
  color: var(--text-muted);
  padding-bottom: var(--size-4-3);
}

.smtcmp-settings-embedding-db-manage-table td {
  padding-top: var(--size-4-3);
  padding-bottom: var(--size-4-3);
  vertical-align: middle;
}

.smtcmp-settings-embedding-db-manage-actions {
  display: flex;
  gap: var(--size-4-2);
}

.smtcmp-settings-embedding-db-manage-actions-loading {
  display: flex;
  align-items: center;
  gap: var(--size-2-2);
  font-size: var(--font-ui-smaller);
}

/* Settings: tables */

.smtcmp-settings-table-container {
  overflow-x: auto;
}

.smtcmp-settings-table {
  margin: var(--size-4-3) 0;
  width: 100%;
  border-collapse: collapse;
}

.smtcmp-settings-table th {
  padding: var(--size-4-1) var(--size-4-1) var(--size-4-2) var(--size-4-1);
  text-align: left;
  vertical-align: middle;
  font-weight: var(--font-medium);
  color: var(--text-muted);
}

.smtcmp-settings-table tbody td {
  height: var(--size-4-10);
  padding: var(--size-4-1);
  text-align: left;
  vertical-align: middle;
}

.smtcmp-settings-table tfoot td {
  padding: var(--size-4-1);
  text-align: right;
  vertical-align: middle;
}

.smtcmp-settings-table thead tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-table tbody tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-actions {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);

  svg {
    height: var(--icon-l);
    width: var(--icon-l);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-settings-table-api-key {
  cursor: pointer;
  color: var(--text-muted);
  font-size: var(--font-ui-small);
}

.smtcmp-settings-table-api-key:hover {
  text-decoration: underline;
}

.smtcmp-error-modal {
  min-width: 60vw;
}

.smtcmp-error-modal-content {
  max-height: 50vh;
  display: flex;
  flex-direction: column;
  user-select: text;
}

.smtcmp-error-modal-message {
  white-space: pre-line;
}

.smtcmp-error-modal-log {
  white-space: pre-wrap;
  word-break: break-word;
  margin-top: 1rem;
  flex-grow: 1;
  overflow-y: auto;
  user-select: text;
  cursor: text;
  font-size: var(--font-ui-small);
}

.smtcmp-error-modal-buttons {
  margin-top: 1rem;
}

.smtcmp-assistant-message-metadata {
  display: flex;
  flex-direction: column;
  margin-top: var(--size-4-1);
  border-left: 2px solid var(--background-modifier-border);
  padding-left: var(--size-4-1);
}

.smtcmp-assistant-message-metadata-content {
  color: var(--text-muted);
  padding-left: var(--size-4-1);
  font-size: var(--font-ui-small);
}

.smtcmp-assistant-message-metadata-annotations {
  display: flex;
  flex-direction: column;
  gap: var(--size-2-1);
}

.smtcmp-assistant-message-metadata-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--size-4-1);
  font-size: var(--font-ui-small);
  color: var(--text-muted);
  background-color: transparent;
  cursor: pointer;
  border-radius: var(--radius-s);
  padding: var(--size-2-3) var(--size-4-1);
  user-select: none;

  &:hover {
    background-color: var(--background-modifier-hover);
  }
}

.smtcmp-assistant-message-metadata-toggle-icon {
  width: var(--size-4-4);
  height: var(--size-4-4);
}

/* Overrides default .markdown-rendered class styles from Obsidian */
.smtcmp-markdown-rendered {
  &.markdown-rendered {
    --render-scale: 1;
    font-size: calc(var(--render-scale) * 1rem);
    line-height: 1.5;

    &.smtcmp-scale-xs {
      --render-scale: 0.8;
    }

    &.smtcmp-scale-sm {
      --render-scale: 0.85;
    }

    &.smtcmp-scale-base {
      --render-scale: 1;
    }

    /* override default variables */
    --p-spacing: calc(var(--render-scale) * 1rem);
    --heading-spacing: calc(var(--p-spacing) * 2.5);
    --checkbox-size: calc(var(--render-scale) * 1rem);
    --checkbox-radius: calc(var(--render-scale) * var(--radius-s));
    --icon-size: calc(var(--render-scale) * var(--icon-m));

    /* adjust list indent */
    --list-indent: 1.5em;

    blockquote {
      padding-inline-start: calc(var(--render-scale) * 1.5rem);
    }

    pre {
      padding: calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 1rem);
    }

    hr {
      margin: calc(var(--render-scale) * 2rem) 0;
    }

    th,
    td {
      font-size: calc(var(--render-scale) * 1rem);
      padding: calc(var(--render-scale) * 0.25rem)
        calc(var(--render-scale) * 0.5rem);
    }

    .callout {
      padding: calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 0.75rem) calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 1.5rem);
    }

    .callout-icon {
      height: calc(var(--render-scale) * 1rem);
      width: calc(var(--render-scale) * 1rem);
    }
  }

  /* Show frontmatter which is hidden by default */
  .frontmatter {
    display: block !important;
    background-color: transparent;
    border-top: 1px solid var(--background-modifier-border);
    border-bottom: 1px solid var(--background-modifier-border);
  }
}

.smtcmp-mention-popover {
  width: 360px;
  max-width: 360px;
}

.smtcmp-mention-popover-folder-path {
  margin-left: auto;
  padding-left: var(--size-4-2);
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  direction: rtl;
  white-space: nowrap;
  color: var(--text-muted);
}

.smtcmp-settings-description-preserve-whitespace {
  .setting-item-description {
    white-space: pre-wrap;
  }
}

.smtcmp-obsidian-code-block {
  pre {
    margin: 0;
  }
}

.smtcmp-toolcall-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  border-radius: var(--radius-s);

  .smtcmp-toolcall-border-top {
    border-top: var(--input-border-width) solid
      var(--background-modifier-border);
  }
}

.smtcmp-toolcall {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--size-4-1);
  padding: var(--size-4-1);
  font-size: var(--font-ui-small);
}

.smtcmp-toolcall-header {
  padding: var(--size-2-3) var(--size-4-1);
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  border-radius: var(--radius-s);
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--background-modifier-hover);
  }

  .smtcmp-toolcall-header-content {
    flex-shrink: 1;
    word-break: break-word;
  }

  .smtcmp-toolcall-header-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .smtcmp-toolcall-header-icon--status {
    margin-left: auto;
  }

  .smtcmp-toolcall-header-tool-name {
    font-family: var(--font-monospace);
    font-weight: 600;
  }
}

.smtcmp-toolcall-content {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--size-4-1);
  max-height: 300px;
  overflow: auto;
  padding: var(--size-4-1);

  .smtcmp-toolcall-content-section {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }
}

.smtcmp-toolcall-footer {
  display: flex;
  justify-content: end;
  align-items: center;
  padding: var(--size-4-1);

  .smtcmp-toolcall-footer-actions {
    display: flex;
    align-items: center;
    gap: var(--size-4-1);

    button {
      background-color: var(--background-primary-alt);
      border: 1px solid var(--background-modifier-border);
      box-shadow: none;

      &:hover {
        background-color: var(--background-modifier-hover);
        border: 1px solid var(--background-modifier-border-hover);
        box-shadow: none;
      }
    }
  }
}

.smtcmp-split-button {
  display: flex;
  align-items: center;

  .smtcmp-split-button-primary {
    border-radius: var(--button-radius) 0 0 var(--button-radius) !important;
  }

  .smtcmp-split-button-toggle {
    padding: var(--size-4-1);
    border-radius: 0 var(--button-radius) var(--button-radius) 0 !important;
    border-left: none !important;
  }
}

/* MCP Section Styles */
.smtcmp-mcp-servers-container {
  display: flex;
  flex-direction: column;
}

.smtcmp-mcp-servers-header {
  display: grid;
  grid-template-columns: 1fr 128px 64px 112px; /* Match columns with .smtcmp-mcp-server-row to ensure proper alignment */
  gap: var(--size-4-4);
  align-items: center;
  min-height: var(--size-4-10);
  border-bottom: var(--border-width) solid var(--background-modifier-border);
  font-weight: var(--font-medium);
  color: var(--text-muted);

  & > div {
    padding: var(--size-4-1);
  }
  & > div:nth-child(1) {
    text-align: left;
  }
  & > div:nth-child(2) {
    text-align: left;
  }
  & > div:nth-child(3) {
    text-align: center;
  }
  & > div:nth-child(4) {
    text-align: center;
  }
}

.smtcmp-mcp-servers-empty {
  padding: var(--size-4-4) 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-muted);
}

.smtcmp-mcp-server {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-mcp-server-row {
  display: grid;
  grid-template-columns: 1fr 128px 64px 112px; /* Match columns with .smtcmp-mcp-servers-header to ensure proper alignment */
  gap: var(--size-4-4);
  align-items: center;
  min-height: var(--size-4-10);

  & > div {
    padding: var(--size-4-1);
  }
}

.smtcmp-mcp-server-name {
  overflow: hidden;
  text-overflow: ellipsis;
}

.smtcmp-mcp-server-status {
  display: flex;
  justify-content: flex-start;
}

.smtcmp-mcp-server-toggle {
  display: flex;
  justify-content: center;
}

.smtcmp-mcp-server-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--size-4-1);

  svg {
    height: var(--icon-l);
    width: var(--icon-l);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-server-expanded-info {
  margin-bottom: var(--size-4-4);
  padding-left: var(--size-4-4);
  border-left: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-server-expanded-info-header {
  font-weight: var(--font-medium);
  margin-bottom: var(--size-4-3);
}

.smtcmp-server-tools-container {
  display: flex;
  flex-direction: column;
  gap: var(--size-4-4);
}

.smtcmp-server-error-message {
  color: var(--text-error);
  font-weight: var(--font-medium);
  font-family: var(--font-monospace);
  font-size: var(--font-ui-small);
  user-select: text;
}

.smtcmp-mcp-server-status-badge {
  border-radius: 9999px;
  padding: var(--size-2-1) var(--size-4-2);
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  line-height: 1;
}

.smtcmp-mcp-server-status-badge-label {
  font-size: var(--font-ui-smaller);
}

.smtcmp-mcp-server-status-badge--connected {
  background-color: color-mix(in srgb, var(--text-success) 10%, transparent);
  color: var(--text-success);
}

.smtcmp-mcp-server-status-badge--connecting {
  background-color: color-mix(in srgb, var(--text-muted) 10%, transparent);
  color: var(--text-muted);
}

.smtcmp-mcp-server-status-badge--error {
  background-color: color-mix(in srgb, var(--text-error) 10%, transparent);
  color: var(--text-error);
}

.smtcmp-mcp-server-status-badge--disconnected {
  background-color: color-mix(in srgb, var(--text-muted) 10%, transparent);
  color: var(--text-muted);
}

.smtcmp-mcp-tool {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--size-4-4);
  align-items: center;
}

.smtcmp-mcp-tool-info {
  display: flex;
  flex-direction: column;
}

.smtcmp-mcp-tool-name {
  font-family: var(--font-monospace);
  font-size: var(--font-ui-smaller);
}

.smtcmp-mcp-tool-description {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

.smtcmp-mcp-tool-toggle {
  display: flex;
  align-items: center;
  gap: var(--size-4-2);
}

.smtcmp-mcp-tool-toggle-label {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

/* MCP Server Modal Styles */
.smtcmp-mcp-server-modal-textarea {
  width: 100%;
  font-family: var(--font-monospace);
  resize: none;
  word-break: break-all;
}

.smtcmp-mcp-server-modal-validation {
  font-family: var(--font-monospace);
  font-size: var(--font-ui-small);
  padding: var(--size-4-1) 0;
  white-space: pre-wrap;

  &.smtcmp-mcp-server-modal-validation--error {
    color: var(--text-error);
  }

  &.smtcmp-mcp-server-modal-validation--success {
    color: var(--text-success);
  }
}

.smtcmp-continue-response-button-container {
  display: flex;
  justify-content: center;
  padding: var(--size-4-1) 0;
}

.smtcmp-continue-response-button {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
}
  /*! Pickr 1.8.4 MIT | https://github.com/Simonwep/pickr */
.pickr{position:relative;overflow:visible;transform:translateY(0)}.pickr *{box-sizing:border-box;outline:none;border:none;-webkit-appearance:none}.pickr .pcr-button{position:relative;height:2em;width:2em;padding:0.5em;cursor:pointer;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;border-radius:.15em;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" stroke="%2342445A" stroke-width="5px" stroke-linecap="round"><path d="M45,45L5,5"></path><path d="M45,5L5,45"></path></svg>') no-repeat center;background-size:0;transition:all 0.3s}.pickr .pcr-button::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:.5em;border-radius:.15em;z-index:-1}.pickr .pcr-button::before{z-index:initial}.pickr .pcr-button::after{position:absolute;content:'';top:0;left:0;height:100%;width:100%;transition:background 0.3s;background:var(--pcr-color);border-radius:.15em}.pickr .pcr-button.clear{background-size:70%}.pickr .pcr-button.clear::before{opacity:0}.pickr .pcr-button.clear:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px var(--pcr-color)}.pickr .pcr-button.disabled{cursor:not-allowed}.pickr *,.pcr-app *{box-sizing:border-box;outline:none;border:none;-webkit-appearance:none}.pickr input:focus,.pickr input.pcr-active,.pickr button:focus,.pickr button.pcr-active,.pcr-app input:focus,.pcr-app input.pcr-active,.pcr-app button:focus,.pcr-app button.pcr-active{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px var(--pcr-color)}.pickr .pcr-palette,.pickr .pcr-slider,.pcr-app .pcr-palette,.pcr-app .pcr-slider{transition:box-shadow 0.3s}.pickr .pcr-palette:focus,.pickr .pcr-slider:focus,.pcr-app .pcr-palette:focus,.pcr-app .pcr-slider:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px rgba(0,0,0,0.25)}.pcr-app{position:fixed;display:flex;flex-direction:column;z-index:10000;border-radius:0.1em;background:#fff;opacity:0;visibility:hidden;transition:opacity 0.3s, visibility 0s 0.3s;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Helvetica Neue",Arial,sans-serif;box-shadow:0 0.15em 1.5em 0 rgba(0,0,0,0.1),0 0 1em 0 rgba(0,0,0,0.03);left:0;top:0}.pcr-app.visible{transition:opacity 0.3s;visibility:visible;opacity:1}.pcr-app .pcr-swatches{display:flex;flex-wrap:wrap;margin-top:0.75em}.pcr-app .pcr-swatches.pcr-last{margin:0}@supports (display: grid){.pcr-app .pcr-swatches{display:grid;align-items:center;grid-template-columns:repeat(auto-fit, 1.75em)}}.pcr-app .pcr-swatches>button{font-size:1em;position:relative;width:calc(1.75em - 5px);height:calc(1.75em - 5px);border-radius:0.15em;cursor:pointer;margin:2.5px;flex-shrink:0;justify-self:center;transition:all 0.15s;overflow:hidden;background:transparent;z-index:1}.pcr-app .pcr-swatches>button::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:6px;border-radius:.15em;z-index:-1}.pcr-app .pcr-swatches>button::after{content:'';position:absolute;top:0;left:0;width:100%;height:100%;background:var(--pcr-color);border:1px solid rgba(0,0,0,0.05);border-radius:0.15em;box-sizing:border-box}.pcr-app .pcr-swatches>button:hover{filter:brightness(1.05)}.pcr-app .pcr-swatches>button:not(.pcr-active){box-shadow:none}.pcr-app .pcr-interaction{display:flex;flex-wrap:wrap;align-items:center;margin:0 -0.2em 0 -0.2em}.pcr-app .pcr-interaction>*{margin:0 0.2em}.pcr-app .pcr-interaction input{letter-spacing:0.07em;font-size:0.75em;text-align:center;cursor:pointer;color:#75797e;background:#f1f3f4;border-radius:.15em;transition:all 0.15s;padding:0.45em 0.5em;margin-top:0.75em}.pcr-app .pcr-interaction input:hover{filter:brightness(0.975)}.pcr-app .pcr-interaction input:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px rgba(66,133,244,0.75)}.pcr-app .pcr-interaction .pcr-result{color:#75797e;text-align:left;flex:1 1 8em;min-width:8em;transition:all 0.2s;border-radius:.15em;background:#f1f3f4;cursor:text}.pcr-app .pcr-interaction .pcr-result::-moz-selection{background:#4285f4;color:#fff}.pcr-app .pcr-interaction .pcr-result::selection{background:#4285f4;color:#fff}.pcr-app .pcr-interaction .pcr-type.active{color:#fff;background:#4285f4}.pcr-app .pcr-interaction .pcr-save,.pcr-app .pcr-interaction .pcr-cancel,.pcr-app .pcr-interaction .pcr-clear{color:#fff;width:auto}.pcr-app .pcr-interaction .pcr-save,.pcr-app .pcr-interaction .pcr-cancel,.pcr-app .pcr-interaction .pcr-clear{color:#fff}.pcr-app .pcr-interaction .pcr-save:hover,.pcr-app .pcr-interaction .pcr-cancel:hover,.pcr-app .pcr-interaction .pcr-clear:hover{filter:brightness(0.925)}.pcr-app .pcr-interaction .pcr-save{background:#4285f4}.pcr-app .pcr-interaction .pcr-clear,.pcr-app .pcr-interaction .pcr-cancel{background:#f44250}.pcr-app .pcr-interaction .pcr-clear:focus,.pcr-app .pcr-interaction .pcr-cancel:focus{box-shadow:0 0 0 1px rgba(255,255,255,0.85),0 0 0 3px rgba(244,66,80,0.75)}.pcr-app .pcr-selection .pcr-picker{position:absolute;height:18px;width:18px;border:2px solid #fff;border-radius:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.pcr-app .pcr-selection .pcr-color-palette,.pcr-app .pcr-selection .pcr-color-chooser,.pcr-app .pcr-selection .pcr-color-opacity{position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:flex;flex-direction:column;cursor:grab;cursor:-webkit-grab}.pcr-app .pcr-selection .pcr-color-palette:active,.pcr-app .pcr-selection .pcr-color-chooser:active,.pcr-app .pcr-selection .pcr-color-opacity:active{cursor:grabbing;cursor:-webkit-grabbing}.pcr-app[data-theme='nano']{width:14.25em;max-width:95vw}.pcr-app[data-theme='nano'] .pcr-swatches{margin-top:.6em;padding:0 .6em}.pcr-app[data-theme='nano'] .pcr-interaction{padding:0 .6em .6em .6em}.pcr-app[data-theme='nano'] .pcr-selection{display:grid;grid-gap:.6em;grid-template-columns:1fr 4fr;grid-template-rows:5fr auto auto;align-items:center;height:10.5em;width:100%;align-self:flex-start}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview{grid-area:2 / 1 / 4 / 1;height:100%;width:100%;display:flex;flex-direction:row;justify-content:center;margin-left:.6em}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-last-color{display:none}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-current-color{position:relative;background:var(--pcr-color);width:2em;height:2em;border-radius:50em;overflow:hidden}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-preview .pcr-current-color::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:.5em;border-radius:.15em;z-index:-1}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette{grid-area:1 / 1 / 2 / 3;width:100%;height:100%;z-index:1}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette .pcr-palette{border-radius:.15em;width:100%;height:100%}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-palette .pcr-palette::before{position:absolute;content:'';top:0;left:0;width:100%;height:100%;background:url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:.5em;border-radius:.15em;z-index:-1}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser{grid-area:2 / 2 / 2 / 2}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity{grid-area:3 / 2 / 3 / 2}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser,.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity{height:0.5em;margin:0 .6em}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-picker,.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-picker{top:50%;transform:translateY(-50%)}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-slider,.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-slider{flex-grow:1;border-radius:50em}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-chooser .pcr-slider{background:linear-gradient(to right, red, #ff0, lime, cyan, blue, #f0f, red)}.pcr-app[data-theme='nano'] .pcr-selection .pcr-color-opacity .pcr-slider{background:linear-gradient(to right, transparent, black),url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');background-size:100%, 0.25em}

undefined
</style>
    </head>
    <body>
<h1 data-heading="Volunteer Absence Analysis - July to September 2025" dir="auto">Volunteer Absence Analysis - July to September 2025</h1>
<h2 data-heading="Overview" dir="auto">Overview</h2>
<p dir="auto">This analysis extracts volunteer availability information from church roster emails to assist with efficient roster planning for the Caboolture Region Uniting Church.</p>
<h2 data-heading="Findings" dir="auto">Findings</h2>
<h3 data-heading="Volunteer Availabilities" dir="auto">Volunteer Availabilities</h3>
<ol>
<li dir="auto">
<p><strong>July 6, 2025</strong> - High Impact Sunday</p>
<ul>
<li dir="auto">Rose Joyner (Worship Leader) - UNAVAILABLE</li>
<li dir="auto">Gail &amp; Chris Pipe-Martin (Cleaning) - UNAVAILABLE  </li>
<li dir="auto">Kirsty &amp; Alice Levis (Offering/Singing) - UNAVAILABLE</li>
<li dir="auto">Cathy Henderson (Bible Reading) - UNAVAILABLE (ongoing)</li>
<li dir="auto">Anonymous volunteer - UNAVAILABLE (medical)</li>
</ul>
</li>
<li dir="auto">
<p><strong>August 10, 2025</strong> - High Impact Sunday</p>
<ul>
<li dir="auto">Rose Joyner (Worship Leader) - UNAVAILABLE</li>
<li dir="auto">Susan &amp; Peter Mortimer (on holidays) - UNAVAILABLE</li>
<li dir="auto">Kirsty &amp; Alice Levis - UNAVAILABLE</li>
</ul>
</li>
<li dir="auto">
<p><strong>September 14, 2025</strong> - Moderate Impact</p>
<ul>
<li dir="auto">Kirsty &amp; Alice Levis - UNAVAILABLE</li>
<li dir="auto">Cathy Henderson - UNAVAILABLE (ongoing)</li>
</ul>
</li>
</ol>
<h3 data-heading="Long-term Concerns" dir="auto">Long-term Concerns</h3>
<ul>
<li dir="auto"><strong>Rose Joyner</strong>: Turning 80 in early September, wants to finish leading worship then or sooner</li>
<li dir="auto"><strong>Cathy Henderson</strong>: Away until November (no Bible readings)</li>
<li dir="auto"><strong>Anonymous Volunteer</strong>: Medical issues affecting entire July-September period</li>
</ul>
<h2 data-heading="Sunday-by-Sunday Availability Overview" dir="auto">Sunday-by-Sunday Availability Overview</h2>
<h3 data-heading="Excellent Availability (Most volunteers available)" dir="auto">Excellent Availability (Most volunteers available)</h3>
<ul>
<li dir="auto"><strong>July 13</strong>: Only Bev Irwin and Cathy Henderson unavailable</li>
<li dir="auto"><strong>July 27</strong>: Only Bev Irwin and Cathy Henderson unavailable</li>
<li dir="auto"><strong>August 3</strong>: Only Bev Irwin and Cathy Henderson unavailable</li>
<li dir="auto"><strong>August 17</strong>: Only Cathy Henderson and Anonymous volunteer unavailable</li>
<li dir="auto"><strong>August 24</strong>: Only Bev Irwin and Cathy Henderson unavailable</li>
<li dir="auto"><strong>August 31</strong>: Only Cathy Henderson and Anonymous volunteer unavailable</li>
<li dir="auto"><strong>September 21</strong>: Only Bev Irwin and Cathy Henderson unavailable</li>
</ul>
<h3 data-heading="Good Availability (Minor gaps)" dir="auto">Good Availability (Minor gaps)</h3>
<ul>
<li dir="auto"><strong>September 7</strong>: Rose Joyner unavailable, but Bev Irwin available</li>
<li dir="auto"><strong>September 28</strong>: Wendy Harvey becomes unavailable, but most others available</li>
</ul>
<h3 data-heading="Challenging Availability (Multiple key volunteers unavailable)" dir="auto">Challenging Availability (Multiple key volunteers unavailable)</h3>
<ul>
<li dir="auto"><strong>July 6</strong>: Rose Joyner, Pipe-Martins, Kirsty &amp; Alice Levis, Cathy Henderson unavailable</li>
<li dir="auto"><strong>July 20</strong>: Kirsty &amp; Alice Levis, Cathy Henderson unavailable (but Rose available)</li>
<li dir="auto"><strong>August 10</strong>: Rose Joyner, Mortimers, Kirsty &amp; Alice Levis, Cathy Henderson unavailable</li>
<li dir="auto"><strong>September 14</strong>: Kirsty &amp; Alice Levis, Cathy Henderson unavailable</li>
</ul>
<h2 data-heading="Volunteer Availability Summary" dir="auto">Volunteer Availability Summary</h2>
<h3 data-heading="Consistently Available (All Sundays July-September)" dir="auto">Consistently Available (All Sundays July-September)</h3>
<ul>
<li dir="auto"><strong>Terry &amp; Bev Saunders</strong>: Available all Sundays (Cleaning, Music, Children's Ministry)</li>
<li dir="auto"><strong>Fay &amp; Ian Laurie</strong>: Available all Sundays (no changes to availability)</li>
</ul>
<h3 data-heading="Specific Availability Patterns" dir="auto">Specific Availability Patterns</h3>
<h4 data-heading="**Rose Joyner** (Worship Leader)" dir="auto"><strong>Rose Joyner</strong> (Worship Leader)</h4>
<ul>
<li dir="auto"><strong>AVAILABLE</strong>: July 13, 20, 27; August 3, 17, 24, 31; September 14, 21, 28</li>
<li dir="auto"><strong>UNAVAILABLE</strong>: July 6, August 10, September 7</li>
<li dir="auto"><strong>NOTE</strong>: Wants to retire by early September (turning 80)</li>
</ul>
<h4 data-heading="**Bev Irwin** (Children's Ministry/Communion/Worship Coordinator)" dir="auto"><strong>Bev Irwin</strong> (Children's Ministry/Communion/Worship Coordinator)</h4>
<ul>
<li dir="auto"><strong>AVAILABLE</strong>: July 6, 20, 27; August 10, 17, 31; September 7, 14</li>
<li dir="auto"><strong>UNAVAILABLE</strong>: All other Sundays in July-September</li>
</ul>
<h4 data-heading="**Susan &amp; Peter Mortimer** (Communion/Collection)" dir="auto"><strong>Susan &amp; Peter Mortimer</strong> (Communion/Collection)</h4>
<ul>
<li dir="auto"><strong>AVAILABLE</strong>: July 6, 13, 20, 27; September 7, 14, 21, 28</li>
<li dir="auto"><strong>UNAVAILABLE</strong>: July 31 - August 18 (holidays)</li>
<li dir="auto"><strong>NOTE</strong>: Peter prefers 2nd Sunday of month only for collection duties</li>
</ul>
<h4 data-heading="**Gail &amp; Chris Pipe-Martin** (Cleaning)" dir="auto"><strong>Gail &amp; Chris Pipe-Martin</strong> (Cleaning)</h4>
<ul>
<li dir="auto"><strong>AVAILABLE</strong>: July 13, 20, 27; All of August; All of September</li>
<li dir="auto"><strong>UNAVAILABLE</strong>: July 6 (available for cleaning from July 12-13)</li>
</ul>
<h4 data-heading="**Kirsty &amp; Alice Levis** (Offering/Communion/Singing)" dir="auto"><strong>Kirsty &amp; Alice Levis</strong> (Offering/Communion/Singing)</h4>
<ul>
<li dir="auto"><strong>AVAILABLE</strong>: July 13, 27; August 3, 17, 24, 31; September 7, 21, 28</li>
<li dir="auto"><strong>UNAVAILABLE</strong>: July 6, 20; August 10; September 14</li>
</ul>
<h4 data-heading="**Wendy Harvey** (General Duties)" dir="auto"><strong>Wendy Harvey</strong> (General Duties)</h4>
<ul>
<li dir="auto"><strong>AVAILABLE</strong>: All Sundays July 6 - September 21</li>
<li dir="auto"><strong>UNAVAILABLE</strong>: September 28 onwards (unavailable Sept 24 - Oct 15)</li>
</ul>
<h3 data-heading="Extended Unavailability" dir="auto">Extended Unavailability</h3>
<ul>
<li dir="auto"><strong>Cathy Henderson</strong> (Bible Readings): Unavailable until November</li>
<li dir="auto"><strong>Anonymous Volunteer</strong>: Unavailable entire July-September period (medical issues)</li>
<li dir="auto"><strong>Kym B</strong>: Removed from all duties (moved to another church)</li>
<li dir="auto"><strong>Peter Weir</strong>: Removed from offering duties (too much for him currently)</li>
</ul>
<h2 data-heading="Recommendations for Roster Planning" dir="auto">Recommendations for Roster Planning</h2>
<ol>
<li dir="auto">
<p><strong>Possible Action Required</strong></p>
<ul class="contains-task-list">
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Find backup worship leader for July 6, August 10, September 7</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Arrange alternative cleaning for early July</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Secure backup for offering duties on multiple Sundays</li>
</ul>
</li>
<li dir="auto">
<p><strong>Succession Planning</strong></p>
<ul class="contains-task-list">
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Request by Rose to train younger volunteers for worship leading</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Identify and train backup Bible readers to cover for Cathy Henderson</li>
</ul>
</li>
<li dir="auto">
<p><strong>Role Adjustments</strong></p>
<ul class="contains-task-list">
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> <strong>Remove Kym B from all duties (moved to another church, reason not provided)</strong></li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Remove Peter Weir from offering duties (too much for him currently)</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox"> Add Dwight Walker to music team and roster email list</li>
</ul>
</li>
</ol>
<h2 data-heading="Files Created" dir="auto">Files Created</h2>
<ol>
<li dir="auto"><code>volunteer_absence_tracker_july_september_2025.csv</code> - Detailed volunteer absence information</li>
<li dir="auto"><code>sunday_roster_availability_matrix_july_september_2025.csv</code> - Week-by-week availability matrix</li>
<li dir="auto">This summary document</li>
</ol>
<h2 data-heading="Next Steps" dir="auto">Next Steps</h2>
<ul class="contains-task-list">
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox">Review these findings with Trinette (roster coordinator)</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox">Begin recruiting and training backup volunteers for critical roles</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox">Confirm availability with volunteers who didn't respond to recent emails</li>
<li data-task="" class="task-list-item" dir="auto"><input type="checkbox" class="task-list-item-checkbox">Plan succession for Rose Joyner's worship leadership role</li>
</ul>
<hr>
<p dir="auto"><em>Analysis completed: June 29, 2025</em><br>
<em>Source: 2025-june-FULL1-roster_inbox_report.CSV</em></p>
    </body>
</html>