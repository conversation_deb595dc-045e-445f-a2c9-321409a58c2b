---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
tags: [para/resources, automation, hotkeys, macos, obsidian, shortcuts]
related: [Templater Auto-Processing Setup Guide, Templates]
---

# Global Hotkey Setup for Obsidian Commands (macOS)

## Quick Setup: Shortcuts App (Recommended)

### 🎯 **Single Command Shortcut**

#### Step 1: Create Shortcut
1. **Open Shortcuts app**
2. **Click "+" → New Shortcut**
3. **Add "Open URLs" action**
4. **Enter URL:**
   ```
   obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater
   ```
5. **Name:** "Process Obsidian Template"
6. **Save**

#### Step 2: Assign Global Hotkey
1. **System Preferences** → **Keyboard** → **Shortcuts**
2. **Services** → Find your shortcut
3. **Assign hotkey:** `⌘⌥T` (Command+Option+T)

### 🚀 **Multi-Command Shortcut (Advanced)**

Create a shortcut that gives you multiple options:

#### Shortcut Actions:
1. **Add "Choose from Menu" action**
2. **Add these options:**
   - "Process Template" 
   - "Create Daily Note"
   - "Open Today's Note"
   - "Insert Template"

3. **For each option, add "Open URLs" with:**

**Process Template:**
```
obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater
```

**Create Daily Note:**
```
obsidian://advanced-uri?vault=cruca-docs&daily=true&template=cruca-documentation/Templates/Enhanced%20Church%20Daily%20Template.md
```

**Open Today's Note:**
```
obsidian://advanced-uri?vault=cruca-docs&daily=true
```

**Insert Template:**
```
obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Ainsert-templater
```

## Alternative Methods

### 🔧 **Method 2: Keyboard Maestro**

If you have Keyboard Maestro:

1. **New Macro** → Name: "Process Obsidian Template"
2. **Trigger:** Hot Key Trigger → `⌘⌥T`
3. **Action:** Open URL → 
   ```
   obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater
   ```

### 🎩 **Method 3: Alfred Workflow**

If you use Alfred with Powerpack:

1. **New Workflow** → Name: "Obsidian Commands"
2. **Hotkey Input** → `⌘⌥T`
3. **Open URL Output** → URL above
4. **Connect hotkey to URL action**

### 📱 **Method 4: Raycast Script**

If you use Raycast:

1. **Create script:** `process-obsidian-template.sh`
2. **Content:**
   ```bash
   #!/bin/bash
   
   # Required parameters:
   # @raycast.schemaVersion 1
   # @raycast.title Process Obsidian Template
   # @raycast.mode compact
   
   open "obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater"
   ```
3. **Assign hotkey in Raycast**

### 🍎 **Method 5: Pure AppleScript**

1. **Script Editor** → New Document
2. **Paste:**
   ```applescript
   tell application "System Events"
       open location "obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater"
   end tell
   ```
3. **Save as Application**
4. **System Preferences** → **Keyboard** → **Shortcuts** → **App Shortcuts**
5. **Add shortcut for your app**

## Recommended Hotkeys

### 🎯 **Suggested Key Combinations:**
- `⌘⌥T` - **Process Template** (T for Template)
- `⌘⌥D` - **Create Daily Note** (D for Daily)
- `⌘⌥O` - **Open Today's Note** (O for Open)
- `⌘⌥I` - **Insert Template** (I for Insert)

### ⚠️ **Avoid These Combinations:**
- `⌘⌥A` - Used by many apps
- `⌘⌥S` - Screenshot shortcuts
- `⌘⌥C` - Common app shortcuts

## Advanced: Multiple Vault Support

If you have multiple Obsidian vaults, create separate shortcuts:

### Personal Vault:
```
obsidian://advanced-uri?vault=personal&commandid=templater-obsidian%3Areplace-in-file-templater
```

### Work Vault:
```
obsidian://advanced-uri?vault=work&commandid=templater-obsidian%3Areplace-in-file-templater
```

### CRUCA Vault:
```
obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater
```

## Troubleshooting

### Issue: Shortcut not working
**Solutions:**
1. **Check vault name** is exactly "cruca-docs"
2. **Ensure Advanced URI plugin** is installed
3. **Test URL in browser** first
4. **Check Obsidian is running**

### Issue: Permission denied
**Solutions:**
1. **System Preferences** → **Security & Privacy** → **Privacy**
2. **Accessibility** → Add Shortcuts app
3. **Automation** → Allow Shortcuts to control other apps

### Issue: Wrong vault opens
**Solutions:**
1. **Check vault name** in URL matches exactly
2. **Use vault path** instead: `&vault=/path/to/vault`
3. **Test with Obsidian URI debugger**

## Testing Your Setup

### 🧪 **Test Steps:**
1. **Open any Obsidian note** with unprocessed template
2. **Press your hotkey** (e.g., `⌘⌥T`)
3. **Verify template processes** automatically
4. **Check all placeholders** are replaced

### 🔍 **Debug URLs:**
Test these in your browser first:

**Basic test:**
```
obsidian://open?vault=cruca-docs
```

**Command test:**
```
obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater
```

## Related Commands

### 📋 **Other Useful Obsidian Commands:**
- `daily-notes:open-today-note` - Open today's daily note
- `templater-obsidian:insert-templater` - Insert template
- `templater-obsidian:create-new-note-from-template` - New note from template
- `workspace:open-today-note` - Open today's note

### 🔗 **URL Examples:**
```
obsidian://advanced-uri?vault=cruca-docs&commandid=daily-notes%3Aopen-today-note
obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Ainsert-templater
```

## Recommended Setup

### 🌟 **Best Practice:**
1. **Use Shortcuts app** for simplicity
2. **Create multi-command shortcut** for flexibility
3. **Assign `⌘⌥T`** for template processing
4. **Test thoroughly** before relying on it

This gives you instant access to Obsidian template processing from anywhere in macOS!
