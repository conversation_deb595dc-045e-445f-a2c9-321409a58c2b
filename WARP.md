# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

## Repository Overview

This is an Obsidian vault containing documentation for Caboolture Region Uniting Church Australia (CRUCA). The vault follows the PARA method (Projects, Areas, Resources, Archive) for organization and includes both administrative documentation and technical documentation for church operations and website development.

## Repository Structure

### Core Directories
- `1-Projects/` - Time-limited goals with defined outcomes
- `2-Areas/` - Ongoing responsibilities (Administration, Hall Hire)
- `3-Resources/` - Reference materials and procedures
- `4-Archive/` - Completed or inactive items
- `0-Journals-Daily Notes/` - Daily work logs and notes
- `Templates/` - Obsidian templates for different note types
- `Website/` - Technical documentation for church website project
- `Office-Procedures/` - Office procedures and documentation
- `Sound-Desk/` - Technical documentation for hardware/software
- `Bulletin/` - Church bulletin related files
- `Training/` - Training materials

### Website Project Structure
The Website folder contains documentation for the CabUCA Church Website:
- Backend: ASP.NET Core 8 API with Entity Framework Core
- Frontend: Vanilla JS for public site, React for admin panel
- Database: SQLite (migrating to PostgreSQL)
- Infrastructure: AWS EC2, <PERSON><PERSON>, NGINX, ModSecurity

## Common Commands

### Obsidian Vault Operations
```bash
# Search for specific documentation
Get-ChildItem -Path . -Recurse -Filter "*.md" | Select-String -Pattern "search_term"

# List all markdown files in a specific area
Get-ChildItem -Path "2-Areas" -Filter "*.md" -Recurse

# Find templates
Get-ChildItem -Path "Templates" -Filter "*.md"
```

### Website Development Commands
```bash
# AWS Parameter Store setup (for website project)
./Website/setup-aws-parameter-store.sh

# Search for website-related documentation
Get-ChildItem -Path "Website" -Recurse -Filter "*.md"
```

## Architecture & Key Concepts

### Documentation System (Obsidian Vault)
- **PARA Method Implementation**: The vault is organized using the PARA (Projects, Areas, Resources, Archive) methodology for knowledge management
- **Templating System**: Enhanced templates with Templater plugin for creating structured notes with metadata
- **Cross-linking**: Notes are interconnected using Obsidian's `[[WikiLink]]` syntax
- **Metadata-driven**: Notes include YAML frontmatter for categorization and Dataview queries

### Church Website Project
- **Multi-tier Architecture**: 
  - API Backend (ASP.NET Core 8)
  - Public Frontend (Vanilla JS)
  - Admin Panel (React)
- **Payment Integration**: Stripe Elements for donation processing
- **Containerization**: Docker-based deployment
- **Security**: ModSecurity WAF, JWT authentication
- **Infrastructure**: AWS-based (EC2, ECR, Parameter Store)

## Key Files & Templates

### Important Documentation
- `README.md` - Main vault overview and navigation
- `Website/README.md` - Website project documentation
- `Templates/README.md` - Template usage guide
- `3-Resources/Hall Hire Procedures.md` - Hall hire procedures
- `Sound-Desk/README.md` - Technical troubleshooting guides

### Template Types
- **Enhanced Daily Template** - Daily notes with metadata and queries
- **Enhanced Project Template** - Project documentation
- **Enhanced Area Template** - Ongoing responsibility documentation
- **Enhanced Resource Template** - Reference material documentation
- **Meeting Template** - Meeting notes with action items
- **Church Report Template** - Church-specific reports

## Working with This Repository

### Creating New Documentation
1. Use the appropriate template from `Templates/` folder
2. Follow the PARA method for file placement
3. Include proper YAML frontmatter metadata
4. Use WikiLink syntax for cross-references: `[[Note Name]]`

### Searching and Navigation
- Use Obsidian's graph view to understand note relationships
- Dataview queries are embedded in templates for dynamic content
- TOC (Table of Contents) files exist for major sections

### Website Development Context
When working on website-related tasks:
- Check `Website/cruca/` for project-specific documentation
- Follow the development guidelines in `Website/README.md`
- AWS secrets are managed via Parameter Store (see setup script)
- Docker containers are used for all services

## Important Notes

### Obsidian-Specific Features
- This is an Obsidian vault, not a traditional code repository
- Files use Markdown with Obsidian extensions (WikiLinks, Dataview queries)
- The `.obsidian` folder contains vault configuration (if present)
- Templates require Templater and Dataview plugins to function properly

### Church Context
- Documentation includes sensitive church administration procedures
- Hall hire procedures and Safe Church policies are key operational documents
- Monthly reports and administrative tasks are tracked in the vault

### Development Practices (for Website)
- Test-driven development approach
- Async by default for backend code
- Docker containers for deployment
- Planning migration from SQLite to PostgreSQL
- Stripe integration for payment processing
