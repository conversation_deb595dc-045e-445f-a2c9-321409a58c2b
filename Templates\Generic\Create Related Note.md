<%*
// Create Related Note Script
// This script helps you create a new note that's automatically linked to the current note

// Get current note information
const currentTitle = tp.file.title;
const currentPath = tp.file.path(true);
const currentFolder = currentPath.substring(0, currentPath.lastIndexOf("/"));
const currentType = tp.frontmatter.type || "note";

// Define template options based on current note type
let templates = [];

if (currentType === "project") {
  templates = [
    { name: "Meeting", path: "Templates/Meeting.md", folder: "", prefix: `${currentTitle} Meeting ` },
    { name: "Resource", path: "Templates/Enhanced Resource.md", folder: "3-Resources", prefix: `${currentTitle} Resource` },
    { name: "Person", path: "Templates/Person.md", folder: "People", prefix: `${currentTitle} Contact` }
  ];
} else if (currentType === "area") {
  templates = [
    { name: "Project", path: "Templates/Enhanced Project.md", folder: "1-Projects", prefix: `${currentTitle} Project` },
    { name: "Resource", path: "Templates/Enhanced Resource.md", folder: "3-Resources", prefix: `${currentTitle} Resource` },
    { name: "Meeting", path: "Templates/Meeting.md", folder: "", prefix: `${currentTitle} Meeting ` }
  ];
} else if (currentType === "resource") {
  templates = [
    { name: "Project", path: "Templates/Enhanced Project.md", folder: "1-Projects", prefix: `${currentTitle} Implementation` },
    { name: "Resource", path: "Templates/Enhanced Resource.md", folder: "3-Resources", prefix: `${currentTitle} Extension` }
  ];
} else if (currentType === "person") {
  templates = [
    { name: "Meeting", path: "Templates/Meeting.md", folder: "", prefix: `Meeting with ${currentTitle} ` },
    { name: "Project", path: "Templates/Enhanced Project.md", folder: "1-Projects", prefix: `${currentTitle} Project` }
  ];
} else if (currentType === "daily") {
  templates = [
    { name: "Meeting", path: "Templates/Meeting.md", folder: "", prefix: `Meeting ` },
    { name: "Project", path: "Templates/Enhanced Project.md", folder: "1-Projects", prefix: `New Project` },
    { name: "Resource", path: "Templates/Enhanced Resource.md", folder: "3-Resources", prefix: `New Resource` }
  ];
} else {
  templates = [
    { name: "Project", path: "Templates/Enhanced Project.md", folder: "1-Projects", prefix: `${currentTitle} Project` },
    { name: "Area", path: "Templates/Enhanced Area.md", folder: "2-Areas", prefix: `${currentTitle} Area` },
    { name: "Resource", path: "Templates/Enhanced Resource.md", folder: "3-Resources", prefix: `${currentTitle} Resource` },
    { name: "Meeting", path: "Templates/Meeting.md", folder: "", prefix: `${currentTitle} Meeting ` },
    { name: "Person", path: "Templates/Person.md", folder: "People", prefix: `${currentTitle} Contact` }
  ];
}

// Prompt user to select a template
const templateChoice = await tp.system.suggester(
  templates.map(t => t.name),
  templates,
  false,
  "Select a template for the related note"
);

if (!templateChoice) {
  // User cancelled
  return;
}

// Get the current date for potential use in filenames
const now = tp.date.now("YYYY-MM-DD");

// Determine the folder and filename
let folder = templateChoice.folder;
let filenamePrefix = templateChoice.prefix || "";

if (templateChoice.name === "Meeting") {
  filenamePrefix += now;
}

// Prompt for filename
const filename = await tp.system.prompt("Enter a name for your related note", filenamePrefix);
if (!filename) {
  // User cancelled
  return;
}

// Determine if we need to create subfolders
let subfolder = "";
if (folder && (folder === "1-Projects" || folder === "2-Areas" || folder === "3-Resources")) {
  const createSubfolder = await tp.system.suggester(
    ["Yes", "No"],
    [true, false],
    false,
    "Create in a subfolder?"
  );
  
  if (createSubfolder) {
    subfolder = await tp.system.prompt("Enter subfolder name", "");
    if (subfolder) {
      folder = `${folder}/${subfolder}`;
    }
  }
}

// Create the full path
let fullPath = filename;
if (folder) {
  fullPath = `${folder}/${filename}`;
}

// Create the note with the selected template
const newFile = await tp.file.create_new(tp.file.find_tfile(templateChoice.path), filename, false, fullPath);

// Wait a moment for the file to be created
await new Promise(resolve => setTimeout(resolve, 500));

// Add the current note to the related field in the new note's frontmatter
const newFilePath = newFile.path;
const newFileContent = await app.vault.read(newFile);

// Parse the frontmatter
const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
const match = newFileContent.match(frontmatterRegex);

if (match) {
  let frontmatter = match[1];
  
  // Check if there's a related field
  if (frontmatter.includes("related:")) {
    // Add the current note to the related field
    frontmatter = frontmatter.replace(/related:\s*\[(.*?)\]/, (match, p1) => {
      const relatedNotes = p1.trim() ? p1.split(",").map(n => n.trim()) : [];
      relatedNotes.push(`"${currentTitle}"`);
      return `related: [${relatedNotes.join(", ")}]`;
    });
  } else {
    // Add a related field with the current note
    frontmatter += `\nrelated: ["${currentTitle}"]`;
  }
  
  // Update the file content
  const updatedContent = newFileContent.replace(frontmatterRegex, `---\n${frontmatter}\n---`);
  await app.vault.modify(newFile, updatedContent);
}

// Now update the current note to link to the new note
const currentFile = tp.file.find_tfile(currentPath);
const currentContent = await app.vault.read(currentFile);

// Check if there's a "Related" section
const relatedSectionRegex = /## Related\n([\s\S]*?)(\n##|$)/;
const relatedMatch = currentContent.match(relatedSectionRegex);

if (relatedMatch) {
  // Add the new note to the Related section
  const relatedSection = relatedMatch[1];
  const updatedRelatedSection = relatedSection + `- [[${filename}]]\n`;
  const updatedContent = currentContent.replace(relatedSectionRegex, `## Related\n${updatedRelatedSection}$2`);
  await app.vault.modify(currentFile, updatedContent);
} else {
  // Add a Related section with the new note
  const updatedContent = currentContent + `\n## Related\n- [[${filename}]]\n`;
  await app.vault.modify(currentFile, updatedContent);
}

// Open the new note
app.workspace.openLinkText(filename, "", true);
%>
