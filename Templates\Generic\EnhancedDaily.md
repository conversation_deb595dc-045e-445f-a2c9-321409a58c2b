---
creation_date: <% tp.date.now("YYYY-MM-DD HH:mm") %>
modification_date: <% tp.date.now("dddd Do MMMM YYYY HH:mm:ss") %>
type: daily
date: <% tp.date.now("YYYY-MM-DD") %>
day_of_week: <% tp.date.now("dddd") %>
week: <% tp.date.now("YYYY-[W]WW") %>
month: <% tp.date.now("YYYY-MM") %>
tags: [daily, <% tp.date.now("YYYY-MM") %>]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# <% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>

<< [[<% tp.date.now("YYYY-MM-DD", -1) %>]] | [[<% tp.date.now("YYYY-MM-DD", 1) %>]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
<%*
// Function to get unfinished tasks from ALL daily notes
async function getAllUnfinishedTasks() {
    const today = tp.date.now("YYYY-MM-DD");
    const dailyNotesFolder = "cruca-documentation/0-Journals-Daily Notes";
    let unfinishedTasks = [];

    try {
        // Get the daily notes folder
        const folder = app.vault.getAbstractFileByPath(dailyNotesFolder);

        if (folder && folder.children) {
            // Process all files in the daily notes folder
            for (const file of folder.children) {
                // Skip if it's not a markdown file or if it's today's note
                if (!file.name.endsWith('.md') || file.name === `${today}.md` || file.name === 'Daily Notes TOC.md') {
                    continue;
                }

                // Extract date from filename (assuming YYYY-MM-DD.md format)
                const dateMatch = file.name.match(/^(\d{4}-\d{2}-\d{2})\.md$/);
                if (!dateMatch) continue;

                const fileDate = dateMatch[1];

                try {
                    const content = await app.vault.read(file);
                    // Match various task formats: - [ ], * [ ], + [ ]
                    // Also handle indented tasks
                    const tasks = content.match(/^[\s]*[-\*\+] \[ \] .+$/gm);

                    if (tasks && tasks.length > 0) {
                        unfinishedTasks.push({
                            date: fileDate,
                            fileName: file.name,
                            tasks: tasks,
                            taskCount: tasks.length
                        });
                    }
                } catch (error) {
                    // File might not be readable, skip it
                    continue;
                }
            }
        }
    } catch (error) {
        // Folder might not exist or be accessible
        console.log("Could not access daily notes folder:", error);
    }

    // Sort by date (most recent first)
    unfinishedTasks.sort((a, b) => b.date.localeCompare(a.date));

    return unfinishedTasks;
}

// Get and display unfinished tasks
const allUnfinishedTasks = await getAllUnfinishedTasks();

if (allUnfinishedTasks.length > 0) {
    const totalTasks = allUnfinishedTasks.reduce((sum, day) => sum + day.taskCount, 0);
    tR += `### 📋 Carried Forward Tasks (${totalTasks} tasks from ${allUnfinishedTasks.length} days)\n`;

    // Show recent tasks (last 7 days) expanded
    const recentTasks = allUnfinishedTasks.filter(day => {
        const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(day.date)) / (1000 * 60 * 60 * 24));
        return daysDiff <= 7;
    });

    const olderTasks = allUnfinishedTasks.filter(day => {
        const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(day.date)) / (1000 * 60 * 60 * 24));
        return daysDiff > 7;
    });

    if (recentTasks.length > 0) {
        tR += "#### 🔥 Recent Tasks (Last 7 Days)\n";
        recentTasks.forEach(dayTasks => {
            const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(dayTasks.date)) / (1000 * 60 * 60 * 24));
            const daysAgo = daysDiff === 1 ? "yesterday" : `${daysDiff} days ago`;
            tR += `##### From ${dayTasks.date} (${daysAgo}) - ${dayTasks.taskCount} tasks\n`;
            dayTasks.tasks.forEach(task => {
                tR += task + "\n";
            });
            tR += "\n";
        });
    }

    if (olderTasks.length > 0) {
        const olderTaskCount = olderTasks.reduce((sum, day) => sum + day.taskCount, 0);
        tR += `#### 📚 Older Tasks (${olderTaskCount} tasks from ${olderTasks.length} days)\n`;
        tR += "<details><summary>Click to expand older tasks</summary>\n\n";

        olderTasks.forEach(dayTasks => {
            const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(dayTasks.date)) / (1000 * 60 * 60 * 24));
            tR += `##### From ${dayTasks.date} (${daysDiff} days ago) - ${dayTasks.taskCount} tasks\n`;
            dayTasks.tasks.forEach(task => {
                tR += task + "\n";
            });
            tR += "\n";
        });

        tR += "</details>\n\n";
    }

    tR += "### ✨ New Tasks for Today\n";
} else {
    tR += "### ✨ Tasks for Today\n";
}
%>
- [ ]

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
- #church 


## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
- #hall-hire 


## Administration
<!-- Administrative tasks, correspondence, documentation -->
- #admin/report 


## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
- #finance 


## safe church
<!-- compliance, training, documentation -->
- #safe-church 

## Bulletin
<!-- compliance, training, documentation -->
- #bulletin 

## Follow-ups
<!-- Items requiring follow-up action -->
#tasks 
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
-


## Notes
<!-- Any other notes or information -->
-


## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(<% tp.date.now("YYYY-MM-DD") %>)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on <% tp.date.now("YYYY-MM-DD") %>
```

## Tasks from This Note
```dataview
TASK
FROM "<% tp.file.path(true) %>"
WHERE !completed
```

## This Week's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(<% tp.date.weekday("YYYY-MM-DD", 0) %>) AND due <= date(<% tp.date.weekday("YYYY-MM-DD", 6) %>)
SORT due ASC
```

## This Month's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(<% tp.date.now("YYYY-MM-01") %>) AND due <= date(<% tp.date.now("YYYY-MM-") + "31" %>)
SORT due ASC
```

## This Quarter's Tasks
<%*
// Calculate current quarter dates
const currentMonth = parseInt(tp.date.now("MM"));
const currentYear = tp.date.now("YYYY");
let quarterStart, quarterEnd;

if (currentMonth <= 3) {
    quarterStart = currentYear + "-01-01";
    quarterEnd = currentYear + "-03-31";
} else if (currentMonth <= 6) {
    quarterStart = currentYear + "-04-01";
    quarterEnd = currentYear + "-06-30";
} else if (currentMonth <= 9) {
    quarterStart = currentYear + "-07-01";
    quarterEnd = currentYear + "-09-30";
} else {
    quarterStart = currentYear + "-10-01";
    quarterEnd = currentYear + "-12-31";
}
%>
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(<% quarterStart %>) AND due <= date(<% quarterEnd %>)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before <% tp.date.now("YYYY-MM-DD") %>
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after <% tp.date.now("YYYY-MM-DD") %>
due before <% tp.date.now("YYYY-MM-DD", 7) %>
```

## Church Administration Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[<% tp.date.now("YYYY-MM-DD") %> Meeting|Create New Meeting]]
- [[<% tp.date.now("YYYY-MM-DD") %> Task|Create New Task]]
- [[<% tp.date.now("YYYY-MM-DD") %> Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[<% tp.date.now("YYYY-MM") %>|Monthly Overview]]
- [[<% tp.date.now("YYYY-[W]WW") %>|Weekly Overview]]
- [[Tasks]]
- [[Home]]
