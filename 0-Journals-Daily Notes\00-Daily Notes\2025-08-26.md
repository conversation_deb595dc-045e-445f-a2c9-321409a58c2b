---
creation_date: 2025-08-26 11:16
modification_date: Tuesday 26th August 2025 11:16:28
type: daily
date: 2025-08-26
day_of_week: Tuesday
week: 2025-W35
month: 2025-08
tags: [daily, 2025-08]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-08-26 - Tuesday

<< [[2025-08-25]] | [[2025-08-27]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ]
- [ ]
- [ ]

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
-
a
## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
- <EMAIL>
	> 12/13 setpember - prefers the friday because she might have to change one of her dates for satuday. She came in requesting the sunday, but that is unavailable due to tongan congregation 
- [ ] Confirm wetgher
	> . and mentioned its possibility now due to BB cessation? -> must confirm this from crcc minutes emails

## Administration
<!-- Administrative tasks, correspondence, documentation -->
- Check for this Unauthorised access of email.
	- Add security actions to #admin/report 
![[Pasted image **************.png]]
- Tried to make app <NAME_EMAIL>, however i need to turn on 2-step verification for that to9 work. I need to advise ian of this prior to doing this to avoid locking out of accounts. #emails 
## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Safe Church
<!-- Compliance, training, documentation -->
-

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Visitor Log
- [ ] Jill Cuffe studio concert asked for dates, mentioned in hall hire notes.
	- [ ] Negotioate possible days to check for avialabilityu.
		- [ ] require outstanding group info on the days`
## Notes
<!-- Any other notes or information -->
-

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-08-26)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-08-26
```

## Tasks from This Note
```dataview
TASK
FROM "cruca-documentation-git/0-Journals-Daily Notes/00-Daily Notes/2025-08-26.md"
WHERE !completed
```

## This Week's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-08-24) AND due <= date(2025-08-30)
SORT due ASC
```

## This Month's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-08-01) AND due <= date(2025-08-31)
SORT due ASC
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-09-30)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-08-26
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-08-26
due before 2025-09-02
```

## Church Administration Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-08-26 Meeting|Create New Meeting]]
- [[2025-08-26 Task|Create New Task]]
- [[2025-08-26 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-08|Monthly Overview]]
- [[2025-W35|Weekly Overview]]
- [[Tasks]]
- [[Home]]
