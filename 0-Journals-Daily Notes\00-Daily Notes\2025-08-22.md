---
creation_date: 2025-08-22 06:31
modification_date: Friday 22nd August 2025 06:31:43
type: daily
date: 2025-08-22
day_of_week: Friday
week: 2025-W34
month: 2025-08
tags:
  - daily
  - 2025-08
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-08-22 - Friday

<< [[2025-08-21]] | [[2025-08-23]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ]
- [ ]
- [ ]

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
- I use outlook new, you can still use the old outlook (in the taskbar)
- because i have added the archive of bigpond emails to the old outlook, i have password protected the archive file. to login to outlook (old) enter: crcc4510
- check the bulletin <NAME_EMAIL> for bulletin notices.
	- check <EMAIL> for any unorganised notices                    that may come through
    - The <EMAIL> email is not organised. It is only a carbon copy of all emails for archive purposes. <EMAIL> is organised using the email aliases (<EMAIL> -> finance folder & <EMAIL> -> bulletin folder <NAME_EMAIL> account
    - check the finance folder for possible emails for the bulletin by kirsty or susan (who usually just email finance now instead of the right email address)
- I have been working on the bulletin in the OneDrive Folder in the Bulletin folder, so that the bulletin is backed up and can be seen by you, fay, me and any other collaborators who want to add notes or assist with the bulletin 
	- (currently Rhiannon, Debbi Chandler, fay, you, and I have been shared the OneDrive Folder Link)
	- The new **Bulletin can be found at:**
		- C:\Users\<USER>\OneDrive\Documents\Bulletin\2025\OneDrive\Bulletins-OneDrive\8 Aug\Bulletin 240825
		- ![[Bulletin 240825.pub]]
- My bulletin folder layout:
 ```
 |2025/OneDrive/Bulletins-OneDrive/8-Aug/Bulletin 240825/
 |__ publish/ (the files that are sent out or printed)
 |__ insert/ (insert
 |
 |- Bu7lletin 240525 <- Your edited Filed
 |- Bulletin 240825.pub (My previous Main Publisher Editing File)
 |- Bulletin 240825.pdf (Draft-Pdf)
 |- Insert4pg-V2-Email.svg (Main Editing Insert file made in inkscape - PDF editor)
 |- insert.pub (same as above, publisher format)
 ```

- So to summarise:
	- bulletin notice emails found in bulletin/ folder
		- check around for stray notices that people didn't <NAME_EMAIL>
	- If you want to use Outlook Old: password is crcc4510
	- Bulletin Folder found at: C:\Users\<USER>\OneDrive\Documents\Bulletin\2025\OneDrive\Bulletins-OneDrive\8 Aug\Bulletin 240825


## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
-

## Administration
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Safe Church
<!-- Compliance, training, documentation -->
-

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-08-22)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-08-22
```

## Tasks from This Note
```dataview
TASK
FROM "cruca-documentation-git/0-Journals-Daily Notes/00-Daily Notes/2025-08-22.md"
WHERE !completed
```

## This Week's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-08-17) AND due <= date(2025-08-23)
SORT due ASC
```

## This Month's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-08-01) AND due <= date(2025-08-31)
SORT due ASC
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-09-30)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-08-22
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-08-22
due before 2025-08-29
```

## Church Administration Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-08-22 Meeting|Create New Meeting]]
- [[2025-08-22 Task|Create New Task]]
- [[2025-08-22 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-08|Monthly Overview]]
- [[2025-W34|Weekly Overview]]
- [[Tasks]]
- [[Home]]
