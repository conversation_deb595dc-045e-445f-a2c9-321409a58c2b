---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: hall-hire
aliases: []
tags: [cruca, church, hall-hire, booking]
client: 
contact_person: 
contact_email: 
contact_phone: 
booking_date: 
booking_time: 
purpose: 
status: inquiry
payment_status: pending
links: []
related: []
---

# <% tp.file.title %>

## Client Information
- **Client/Organization**: 
- **Contact Person**: 
- **Email**: 
- **Phone**: 

## Booking Details
- **Date**: 
- **Time**: 
- **Purpose**: 
- **Expected Attendance**: 
- **Special Requirements**: 

## Fees
- **Hourly Rate**: $
- **Total Hours**: 
- **Total Fee**: $
- **Deposit Required**: $
- **Balance Due**: $

## Status
- **Booking Status**: Inquiry
- **Payment Status**: Pending
- **Agreement Signed**: No
- **Insurance Provided**: No

## Communication Log
- <% tp.date.now("YYYY-MM-DD") %> - Initial inquiry received

## Tasks
- [ ] Send hall hire information and rates
- [ ] Confirm availability
- [ ] Send booking agreement
- [ ] Collect deposit
- [ ] Receive signed agreement
- [ ] Verify insurance
- [ ] Collect balance payment
- [ ] Schedule induction
- [ ] Provide access information

## Notes
- 

## Related
- [[Administration/Hall Hire]]
- [[CRUCA-Church-Vault/Administration 1/Admin-Documentation-Guides/Hall Hire Procedures/Hall Hire Procedures]]
