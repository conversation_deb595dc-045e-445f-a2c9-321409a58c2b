---
creation_date: 2025-07-10
modification_date: 2025-07-10
type: area
status: active
area: Administration
area_owner: Jordan
responsibility_level: high
review_frequency: weekly
tags: [para/areas, administration]
last_review_date: 2025-07-10
next_review_date: 2025-08-09
---

# Unity Water Account Details for CRUCA

## Overview
<!-- Brief description of this area of responsibility -->
#unitywater #account/details

|                       |                                                                                                                                      |     |                 |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------------ | --- | --------------- |
| #account/details**    | **Property Address as listed on Unity Water Bills  (Caboolture Region Uniting Church — Uniting Church in Australia property Trust)** |     | #account/number |
| **Billing Account 1** | 561 Caboolture River road, Upper Caboolture                                                                                          |     | ************    |
|                       |                                                                                                                                      |     |                 |
| **Billing Account 2** | Caboolture Uniting Church, 2-8 Smiths Road, Caboolture, QLD                                                                          |     | **1004192**     |
## Current Focus
<!-- What's the current focus in this area? -->
-

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily:
- [ ] Weekly:
- [ ] Monthly:

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "Administration" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.content, "[[Unity Water Account Details for CRUCA]]") OR area = "Administration"
SORT file.mtime DESC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[Unity Water Account Details for CRUCA]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[Unity Water Account Details for CRUCA Project|New Project]]
- [[Unity Water Account Details for CRUCA Resource|New Resource]]
- [[Unity Water Account Details for CRUCA Meeting|New Meeting]]
- [[2-Areas|All Areas]]
