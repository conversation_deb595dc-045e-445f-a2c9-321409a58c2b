---
creation date: 2025-04-21 23:12
modification date: Monday 21st April 2025 23:12:09
type: daily
date: 2025-04-21
day_of_week: Monday
week: 2025-W17
month: 2025-04
tags: [daily, 2025-04]
mood: ""
energy_level: ""
weather: ""
location: ""
---
# 2025-04-21 - Monday

<< [[2025-04-20]] | [[2025-04-22]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- 

## Tasks
#todo #tasks #outstanding 
**General**
- [ ] **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mailfrom post office
- [ ] Change the 2025 CRUC Calendar - Events by X spreadsheet. :
	- [ ] swap the pacific combined dinner from 9 aug to 16th of august to accomodate the new bunnings saus <PERSON> has mentioned will be swapping
- [ ] order new business cards for information update 
- [ ] add the upper caboolture craft section to cruca.org website
- [ ] Bank Craft Stall Money
- [ ] timesheet to fill for CRCC meetings
- [ ] check Koorong
- [ ] Transfer the 100k on friday as there is 100k limit on bank acc
**Accounts:**
- [ ] Email Telstra and contact to update details to get invoices sent better method
- [ ] contacts? ********** from 11/01 & **********
**Finance**
- [ ] Need to write up payment vouchers for the payments made today:
- [ ] ![[cruca-documentation/2-Areas/Finance Billing/May/Pasted image **************.png]]
## Journal
<!-- How was your day? What happened? What did you learn? -->
**Finance Work**
from 'withdrawel dockets missing' list:
- 04/04 $27 listed as synod: this is xerox subscription? need to ask susan that i do not see invoices for xero
- transfer 100k
- incorrect cash deposits by Ian
	- Need to serach the bank to see which dates it was on using the amount as a key
- need to 
**General**

**Meetings**
- Words for worship called saying they sent invoice and boiunced back from bigpond. I changed the contact <NAME_EMAIL>
- 
## Notes
<!-- Any other notes or information -->
- email kym to confirm her request?
- website
- #rosters Wendekll/lyn: lyn for weclome, weoship cord for wendell offering counting wendell

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-21)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-21
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-21
due before 2025-04-28
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-21 Meeting|Create New Meeting]]
- [[2025-04-21 Task|Create New Task]]
- [[2025-04-21 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W17|Weekly Overview]]
- [[Tasks]]
- [[Home]]
