Here’s your power-user cheat sheet for managing **virtual desktops (workspaces)** in Windows 11:

---

### 🧭 Enter Workspace (Task View)
- **`Win + Tab`** → Opens **Task View**, where you can see all open windows and desktops.

---

### 🔁 Cycle Through Workspaces
- **`Ctrl + Win + Left/Right Arrow`** → Switch between virtual desktops.

---

### 🚚 Move a Window to Another Workspace
Unfortunately, **there’s no direct keyboard shortcut** to move a window between desktops. But here’s the fastest method:

1. Press **`Win + Tab`** to open Task View.
2. Drag the window to the desired desktop at the top of the screen.

> Tip: If you're automating workflows or using scripting tools like PowerToys or AutoHotkey, you can simulate this behavior with custom scripts.

---

Want help setting up a workspace layout for your bulletin prep, roster automation, and troubleshooting tasks? I can help you design a clean, efficient desktop flow.
 