# CabUCA Church Website Documentation

This folder contains documentation for the CabUCA Church Website project.

## Contents

- [Secrets Management](./secrets-management.md) - Documentation on how secrets and configuration values are managed in the project
- [Setup AWS Parameter Store](./setup-aws-parameter-store.sh) - <PERSON><PERSON><PERSON> to help set up AWS Parameter Store for production

## Development Guidelines

### Code Style

- Private class variables start with underscore
- Constants use UPPER_SNAKE_CASE
- Single quotes for strings
- Functions should be small and focused with descriptive names
- Documentation should be meaningful and up-to-date

### Development Practices

- Test-driven development with tests written first
- Clean, maintainable code with meaningful documentation
- Async by default unless unnecessary
- Error handling only for specific exceptions
- Logging using a logger system

### Deployment

- Docker containers for services (React admin, API with frontend, NGINX, ModSecurity)
- Images pushed to AWS ECR and deployed to AWS EC2
- Planning to improve deployment, testing, logging, maintenance, and backups

## Project Structure

- Backend: `backend/CabUCA.API/CabUCA.API`
- Frontend: `frontend/`
- Admin Panel: `admin-frontend/`

## Tech Stack

- Backend: ASP.NET Core 8 API, Entity Framework Core, JWT authentication
- Frontend: Vanilla JS for public site, React for admin panel
- Database: SQLite (migrating to PostgreSQL)
- Infrastructure: AWS EC2, Docker containers, NGINX, ModSecurity, AWS ECR for image registry

## Features

- Donation system using Stripe Elements for payment processing
- Backend handles payment intents and webhooks
- Donations stored in database with admin panel interface
- SPA-like architecture for frontend with dynamic content loading
