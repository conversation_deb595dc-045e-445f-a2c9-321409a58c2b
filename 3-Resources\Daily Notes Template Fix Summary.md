---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
tags: [para/resources, fix, templates, daily-notes, templater]
related: [Templates, Daily Notes TOC]
---

# Daily Notes Template Fix Summary

## Problem Identified
Your daily notes were showing **hardcoded April 2025 dates** instead of updating dynamically to the current date. This was because the `EnhancedDaily.md` template had static dates instead of dynamic Templater syntax.

## Root Cause
The `EnhancedDaily.md` template (which appears to be your active daily note template) contained:

### ❌ **Before (Broken - Static Dates):**
```yaml
---
creation date: 2025-04-21 23:12
modification date: Monday 21st April 2025 23:12:09
type: daily
date: 2025-04-21
day_of_week: Monday
week: 2025-W17
month: 2025-04
tags: [daily, 2025-04]
---

# 2025-04-21 - Monday

<< [[2025-04-20]] | [[2025-04-22]] >>
```

### ✅ **After (Fixed - Dynamic Dates):**
```yaml
---
creation_date: <% tp.date.now("YYYY-MM-DD HH:mm") %>
modification_date: <% tp.date.now("dddd Do MMMM YYYY HH:mm:ss") %>
type: daily
date: <% tp.date.now("YYYY-MM-DD") %>
day_of_week: <% tp.date.now("dddd") %>
week: <% tp.date.now("YYYY-[W]WW") %>
month: <% tp.date.now("YYYY-MM") %>
tags: [daily, <% tp.date.now("YYYY-MM") %>]
---

# <% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>

<< [[<% tp.date.now("YYYY-MM-DD", -1) %>]] | [[<% tp.date.now("YYYY-MM-DD", 1) %>]] >>
```

## Templates Fixed
I identified and fixed **multiple daily note templates** that had the same issue:

1. **`cruca-documentation/Templates/EnhancedDaily.md`** ✅ Fixed
2. **`cruca-documentation/Templates/Generic/EnhancedDaily.md`** ✅ Fixed

## Other Templates Checked
These templates were already using proper dynamic syntax:
- ✅ `Daily Note Template.md` - Already correct
- ✅ `Daily.md` - Already correct  
- ✅ `Daily Note.md` - Already correct (but this is more of a general note template)

## What Was Fixed

### 1. **Metadata Fields**
- `creation_date`: Now uses `<% tp.date.now("YYYY-MM-DD HH:mm") %>`
- `modification_date`: Now uses `<% tp.date.now("dddd Do MMMM YYYY HH:mm:ss") %>`
- `date`: Now uses `<% tp.date.now("YYYY-MM-DD") %>`
- `day_of_week`: Now uses `<% tp.date.now("dddd") %>`
- `week`: Now uses `<% tp.date.now("YYYY-[W]WW") %>`
- `month`: Now uses `<% tp.date.now("YYYY-MM") %>`
- `tags`: Now includes dynamic month `<% tp.date.now("YYYY-MM") %>`

### 2. **Title and Navigation**
- Title: Now uses `<% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>`
- Previous/Next links: Now use `<% tp.date.now("YYYY-MM-DD", -1) %>` and `<% tp.date.now("YYYY-MM-DD", 1) %>`

### 3. **Dataview Queries**
- Today's meetings: Now uses `date(<% tp.date.now("YYYY-MM-DD") %>)`
- Today's tasks: Now uses `<% tp.date.now("YYYY-MM-DD") %>`
- Upcoming tasks: Now uses date ranges with `<% tp.date.now("YYYY-MM-DD", 7) %>`

### 4. **Related Links**
- Monthly overview: Now uses `<% tp.date.now("YYYY-MM") %>`
- Weekly overview: Now uses `<% tp.date.now("YYYY-[W]WW") %>`
- Create new notes: Now use current date `<% tp.date.now("YYYY-MM-DD") %>`

## How to Test
1. **Create a new daily note** using the calendar plugin or daily notes command
2. **Check that all dates** in the metadata, title, and content reflect today's date
3. **Verify dataview queries** show today's meetings and tasks
4. **Confirm navigation links** point to yesterday and tomorrow correctly

## Template Selection
If you're still having issues, you may need to check which template is actually being used:

1. **Check Daily Notes Settings**: Go to Settings → Daily notes → Template file path
2. **Check Calendar Plugin Settings**: Go to Settings → Calendar → Daily note template
3. **Check Templater Settings**: Go to Settings → Templater → Template folder path

## Recommended Template
Based on your usage pattern, I recommend using **`Daily Note Template.md`** as it's:
- ✅ Church-focused with sections for Church Matters, Hall Hire, Administration
- ✅ Already using proper dynamic syntax
- ✅ Simpler and more focused than EnhancedDaily
- ✅ Designed specifically for your church administration needs

## Next Steps
1. **Test creating a new daily note** to confirm the fix works
2. **Consider switching to `Daily Note Template.md`** if you prefer the church-focused layout
3. **Update your daily notes settings** to point to your preferred template
4. **Let me know if you need help** configuring which template to use

## NEW: Enhanced Church Daily Template ⭐

I've created a **brand new template** that combines the best of both worlds:

### 📋 **Features:**
- ✅ **Automatic Task Rollover**: Carries forward unfinished tasks from the last 7 days
- ✅ **Church-Focused Sections**: All the sections from Daily Note Template
- ✅ **Advanced Dataview Queries**: Smart task and event tracking
- ✅ **Dynamic Dates**: Properly configured Templater syntax
- ✅ **Enhanced Task Management**: Multiple task views and filtering

### 🔄 **Task Rollover System:**
The template automatically:
1. **Scans the last 7 days** for unfinished tasks
2. **Groups tasks by date** they originated from
3. **Displays them clearly** under "Carried Forward Tasks"
4. **Handles multiple task formats**: `- [ ]`, `* [ ]`, `+ [ ]`

### 🏛️ **Church Administration Sections:**
- **Church Matters**: Services, pastoral care, meetings
- **Hall Hire**: Bookings, inquiries, key management
- **Administration**: Reports, correspondence, documentation
- **Finance & Banking**: Donations, payments, reconciliation
- **Safe Church**: Compliance, training, Blue Card updates
- **Communication**: Emails, website, social media
- **Facilities & Maintenance**: Building, equipment, security

### 📊 **Smart Dataview Features:**
- **Today's Events & Reminders**: Shows events scheduled for today
- **Weekly Recurring Events**: Shows activities for current day of week
- **Church Administration Tasks**: Filtered tasks from your vault
- **Overdue Tasks**: Tasks that need immediate attention
- **Active Projects**: Current project status and progress

## Template Recommendations

### 🌟 **Best Choice: Enhanced Church Daily Template**
**File**: `Templates/Enhanced Church Daily Template.md`
- **Perfect for**: Church administrators managing multiple responsibilities
- **Features**: Task rollover + Church sections + Advanced queries
- **Use when**: You want comprehensive daily planning with automatic task management

### 🏛️ **Simple Choice: Daily Note Template**
**File**: `Templates/Daily Note Template.md`
- **Perfect for**: Basic daily church administration
- **Features**: Church sections + Simple layout
- **Use when**: You prefer simplicity without advanced features

### 📈 **Advanced Choice: EnhancedDaily (Fixed)**
**File**: `Templates/EnhancedDaily.md`
- **Perfect for**: Power users who want maximum dataview features
- **Features**: Advanced queries + Task rollover + Church sections
- **Use when**: You want all the bells and whistles

## How to Switch Templates

1. **Go to Settings** → **Daily notes**
2. **Set Template file path** to your preferred template:
   - `cruca-documentation/Templates/Enhanced Church Daily Template.md` (Recommended)
   - `cruca-documentation/Templates/Daily Note Template.md` (Simple)
   - `cruca-documentation/Templates/EnhancedDaily.md` (Advanced)

## Related Files
- [[Templates/Enhanced Church Daily Template]] - **NEW: Recommended complete template**
- [[Templates/Daily Note Template]] - Simple church-focused template
- [[Templates/EnhancedDaily]] - Fixed enhanced template with dataview queries
- [[Templates/Daily]] - Basic daily template
- [[Daily Notes TOC]] - Daily notes overview
- [[Templates TOC]] - All available templates
