---
creation_date: 2025-07-03 11:28
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: area
status: active
area: 
tags:
  - para/areas
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
- 

## Tasks
<!-- Ongoing tasks in this area -->
- [ ] 

## Related Projects
<!-- Links to related projects -->
```dataview
LIST
FROM [[<% tp.file.title %>]] AND "1-Projects"
```

## Resources
<!-- Links to relevant resources -->
- 

## Notes
<!-- Any additional notes -->
#finance/procedures
Add to the weekly procedures board:
1. square
2. chec bankings
3. 
4. Setting up payments
	1. adding the payment details to kirstys email as i go
	2. printing bank transfers

##### #Susan's #Procedures for #payment from email #27/06
Good afternoon jordan,
	<PERSON> had picked up the dockets today for me.
	
	He said they were all on your desk. As you authorized or set these up yesterday they should have been in the data entry tray.
	
	The procedure as discussed yesterday
	
	1. Get the invoice posted or email to authorize payment - these should be paper copies
	
	2. Print bank authorisation sheet as you set it up
	
	3..Stamp as paid with the date.
	
	4.staple together
	
	3. Put in the data entry tray.
	
	4. If invoices were received on email transfer them to the paid folder - to avoid double payments.
	
	That way they are then off your desk. 
	
	Each payment must be printed separately.
	
	I do not have 
	
	5. the telstra bill that was received in the post - it is blue in colour
	
	6. The authorisation for the day camp payment to Kirsty - nor the email request for payment
	
	7. The additional payment to the other person - should have the email where the account details are supplied and the bank authorisation as you do them.
	
	SUSAN
## Related
- [[2-Areas]]

# <% tp.file.title %>

## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
-

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily:
- [ ] Weekly:
- [ ] Monthly:

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>"
SORT file.mtime DESC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[<% tp.file.title %>]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[2-Areas|All Areas]]
